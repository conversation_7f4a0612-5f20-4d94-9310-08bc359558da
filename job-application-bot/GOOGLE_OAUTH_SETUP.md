# Google OAuth Setup Guide

## Quick Fix for Gmail Login Issues

### Step 1: Create Google OAuth Credentials

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create or Select a Project**
   - Click "Select a project" at the top
   - Click "New Project" if you don't have one
   - Name it "JobBot" or similar
   - Click "Create"

3. **Enable Google+ API**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API"
   - Click on it and press "Enable"

4. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Name: "JobBot Web Client"
   - **Authorized JavaScript origins:**
     - `http://localhost:3000`
   - **Authorized redirect URIs:**
     - `http://localhost:3000/api/auth/callback/google`
   - Click "Create"

5. **Copy Your Credentials**
   - Copy the "Client ID" and "Client Secret"

### Step 2: Update Environment Variables

1. **Edit `.env.local` file:**
   ```bash
   # Replace with your actual credentials
   GOOGLE_CLIENT_ID="your-client-id-here.apps.googleusercontent.com"
   GOOGLE_CLIENT_SECRET="your-client-secret-here"
   ```

2. **Generate a secure secret:**
   ```bash
   # Run this command to generate a secure secret
   openssl rand -base64 32
   ```
   
   Then update:
   ```bash
   NEXTAUTH_SECRET="your-generated-secret-here"
   ```

### Step 3: Restart the Development Server

```bash
npm run dev
```

### Step 4: Test the Login

1. Go to http://localhost:3000
2. Click "Sign in to get started"
3. Click "Continue with Google"
4. You should be redirected to Google's OAuth consent screen

## Common Issues and Solutions

### Issue 1: "Error 400: redirect_uri_mismatch"
**Solution:** Make sure your redirect URI in Google Console exactly matches:
`http://localhost:3000/api/auth/callback/google`

### Issue 2: "Error 403: access_blocked"
**Solution:** 
- Your app is in testing mode
- Add your email to "Test users" in Google Console
- Go to "OAuth consent screen" > "Test users" > "Add users"

### Issue 3: "Configuration Error"
**Solution:** Check that your environment variables are set correctly:
```bash
# Check if variables are loaded
echo $GOOGLE_CLIENT_ID
echo $GOOGLE_CLIENT_SECRET
echo $NEXTAUTH_SECRET
```

### Issue 4: Still not working?
**Fallback Option:** Use email sign-in instead:
1. The app supports email-based authentication
2. Click "Send sign-in link" on the login page
3. Check your email for the magic link

## Production Setup

For production deployment, update:
1. **Authorized JavaScript origins:** Add your production domain
2. **Authorized redirect URIs:** Add your production callback URL
3. **Environment variables:** Update NEXTAUTH_URL to your production URL

## Need Help?

If you're still having issues:
1. Check the browser console for error messages
2. Check the terminal/server logs
3. Verify all environment variables are set
4. Make sure the Google Cloud project is active

The authentication system is now simplified and should work with basic JWT sessions even without a database connection.
