// User types
export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// Resume types
export interface Resume {
  id: string;
  userId: string;
  filename: string;
  originalText: string;
  parsedData: ResumeData;
  fileUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface ResumeData {
  personalInfo: {
    name: string;
    email: string;
    phone?: string;
    location?: string;
    linkedin?: string;
    github?: string;
    website?: string;
  };
  summary?: string;
  experience: WorkExperience[];
  education: Education[];
  skills: string[];
  projects?: Project[];
  certifications?: Certification[];
}

export interface WorkExperience {
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string[];
  location?: string;
}

export interface Education {
  institution: string;
  degree: string;
  field?: string;
  startDate: string;
  endDate?: string;
  gpa?: string;
  location?: string;
}

export interface Project {
  name: string;
  description: string;
  technologies: string[];
  url?: string;
  github?: string;
  startDate?: string;
  endDate?: string;
}

export interface Certification {
  name: string;
  issuer: string;
  date: string;
  expiryDate?: string;
  credentialId?: string;
  url?: string;
}

// Job types
export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  salary?: string;
  description: string;
  requirements: string[];
  benefits?: string[];
  url: string;
  platform: JobPlatform;
  postedDate: string;
  expiryDate?: string;
  remote: boolean;
  experienceLevel: ExperienceLevel;
  jobType: JobType;
}

export type JobPlatform = 'workatastartup' | 'linkedin' | 'indeed' | 'glassdoor';
export type ExperienceLevel = 'entry' | 'mid' | 'senior' | 'executive';
export type JobType = 'full-time' | 'part-time' | 'contract' | 'internship';

// Job search types
export interface JobSearchCriteria {
  jobTitle: string;
  location?: string;
  remote?: boolean;
  experienceLevel?: ExperienceLevel;
  jobType?: JobType;
  minSalary?: number;
  maxSalary?: number;
  platforms: JobPlatform[];
  keywords?: string[];
}

export interface JobSearchResult {
  jobs: Job[];
  totalCount: number;
  platform: JobPlatform;
  searchCriteria: JobSearchCriteria;
  searchDate: string;
}

// Application types
export interface JobApplication {
  id: string;
  userId: string;
  jobId: string;
  job: Job;
  resumeId: string;
  coverLetter?: string;
  tailoredResume?: string;
  status: ApplicationStatus;
  appliedDate: string;
  lastUpdated: string;
  notes?: string;
  followUpDate?: string;
  interviewDate?: string;
  rejectionReason?: string;
}

export type ApplicationStatus = 
  | 'draft'
  | 'submitted' 
  | 'pending'
  | 'under_review'
  | 'interview_scheduled'
  | 'interviewed'
  | 'offer_received'
  | 'accepted'
  | 'rejected'
  | 'withdrawn';

// Settings types
export interface UserSettings {
  id: string;
  userId: string;
  autoApply: boolean;
  emailNotifications: boolean;
  dailyApplicationLimit: number;
  preferredJobTypes: JobType[];
  preferredExperienceLevel: ExperienceLevel;
  blacklistedCompanies: string[];
  preferredLocations: string[];
  remoteOnly: boolean;
  minSalary?: number;
  maxApplicationsPerDay: number;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface ResumeUploadForm {
  file: File;
}

export interface JobSearchForm {
  jobTitle: string;
  location: string;
  experienceLevel: ExperienceLevel;
  minSalary: number;
  platforms: JobPlatform[];
}

export interface ApplicationForm {
  jobId: string;
  customCoverLetter?: string;
  notes?: string;
}
