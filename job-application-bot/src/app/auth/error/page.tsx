"use client";

import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case "Configuration":
        return {
          title: "Configuration Error",
          message: "There is a problem with the server configuration. Please contact support.",
          details: "The authentication provider is not properly configured."
        };
      case "AccessDenied":
        return {
          title: "Access Denied",
          message: "You do not have permission to sign in.",
          details: "Your account may not be authorized to access this application."
        };
      case "Verification":
        return {
          title: "Verification Error",
          message: "The verification token has expired or has already been used.",
          details: "Please try signing in again."
        };
      case "OAuthSignin":
        return {
          title: "OAuth Sign-in Error",
          message: "Error in constructing an authorization URL.",
          details: "There was an issue with the OAuth provider configuration."
        };
      case "OAuthCallback":
        return {
          title: "OAuth Callback Error",
          message: "Error in handling the response from an OAuth provider.",
          details: "The OAuth provider returned an error or invalid response."
        };
      case "OAuthCreateAccount":
        return {
          title: "OAuth Account Creation Error",
          message: "Could not create OAuth account in the database.",
          details: "There was an issue creating your account. Please try again."
        };
      case "EmailCreateAccount":
        return {
          title: "Email Account Creation Error",
          message: "Could not create email account in the database.",
          details: "There was an issue creating your account with email sign-in."
        };
      case "Callback":
        return {
          title: "Callback Error",
          message: "Error in the OAuth callback handler route.",
          details: "There was an issue processing the authentication callback."
        };
      case "OAuthAccountNotLinked":
        return {
          title: "Account Not Linked",
          message: "The email on the account is already linked, but not with this OAuth account.",
          details: "Try signing in with the method you used originally."
        };
      case "EmailSignin":
        return {
          title: "Email Sign-in Error",
          message: "The e-mail could not be sent.",
          details: "Please check your email configuration or try a different sign-in method."
        };
      case "CredentialsSignin":
        return {
          title: "Credentials Sign-in Error",
          message: "The authorize callback returned null.",
          details: "Please check your credentials and try again."
        };
      case "SessionRequired":
        return {
          title: "Session Required",
          message: "The content of this page requires you to be signed in at all times.",
          details: "Please sign in to continue."
        };
      default:
        return {
          title: "Authentication Error",
          message: "An unknown error occurred during authentication.",
          details: error ? `Error code: ${error}` : "Please try again or contact support."
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-red-600" />
          </div>
          <CardTitle className="text-red-900">{errorInfo.title}</CardTitle>
          <CardDescription className="text-red-700">
            {errorInfo.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-800">{errorInfo.details}</p>
          </div>
          
          <div className="space-y-2">
            <Button asChild className="w-full">
              <Link href="/auth/signin">
                Try Again
              </Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Link>
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <p className="text-xs text-gray-600 font-mono">
                Debug Info: {error || 'No error code provided'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
