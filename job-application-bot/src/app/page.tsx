"use client";

import { useSession } from "next-auth/react";
import { Dashboard } from "@/components/dashboard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Briefcase, Loader2 } from "lucide-react";
import { signIn } from "next-auth/react";

export default function Home() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 font-medium">Loading JobBot...</p>
        </div>
      </div>
    );
  }

  // Bypass login in development mode
  if (!session && process.env.NODE_ENV === 'development') {
    // Create a mock session for development
    const mockSession = {
      user: {
        id: 'dev-user',
        email: '<EMAIL>',
        name: 'Developer User',
        image: null
      }
    };
    return <Dashboard session={mockSession} />;
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                <Briefcase className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Welcome to JobBot
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2 leading-relaxed">
              Automate your job applications with AI-powered resume tailoring and comprehensive email tracking
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <Button
              onClick={() => signIn()}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Sign in to get started
            </Button>
            <p className="text-xs text-gray-500 text-center mt-4">
              Secure authentication powered by NextAuth.js
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <Dashboard />;
}
