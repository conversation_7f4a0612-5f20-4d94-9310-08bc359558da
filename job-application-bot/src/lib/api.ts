import { 
  ApiResponse, 
  JobSearchCriteria, 
  JobSearchResult, 
  JobApplication, 
  ApplicationForm 
} from '@/types';

const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-domain.com/api' 
  : 'http://localhost:3000/api';

// Generic API call function
async function apiCall<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || 'An error occurred',
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('API call error:', error);
    return {
      success: false,
      error: 'Network error occurred',
    };
  }
}

// Resume API functions
export async function uploadResume(file: File): Promise<ApiResponse<any>> {
  const formData = new FormData();
  formData.append('resume', file);

  try {
    const response = await fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || 'Upload failed',
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Upload error:', error);
    return {
      success: false,
      error: 'Upload failed',
    };
  }
}

// Job search API functions
export async function searchJobs(
  criteria: JobSearchCriteria
): Promise<ApiResponse<JobSearchResult>> {
  return apiCall<JobSearchResult>('/jobs/search', {
    method: 'POST',
    body: JSON.stringify(criteria),
  });
}

// Application API functions
export async function getApplications(): Promise<ApiResponse<JobApplication[]>> {
  return apiCall<JobApplication[]>('/applications');
}

export async function submitApplication(
  applicationData: ApplicationForm
): Promise<ApiResponse<JobApplication>> {
  return apiCall<JobApplication>('/applications', {
    method: 'POST',
    body: JSON.stringify(applicationData),
  });
}

export async function updateApplicationStatus(
  applicationId: string,
  status: string,
  notes?: string
): Promise<ApiResponse<any>> {
  return apiCall('/applications', {
    method: 'PUT',
    body: JSON.stringify({
      applicationId,
      status,
      notes,
    }),
  });
}

// Settings API functions
export async function getUserSettings(): Promise<ApiResponse<any>> {
  return apiCall('/settings');
}

export async function updateUserSettings(settings: any): Promise<ApiResponse<any>> {
  return apiCall('/settings', {
    method: 'PUT',
    body: JSON.stringify(settings),
  });
}

// Analytics API functions
export async function getApplicationStats(): Promise<ApiResponse<any>> {
  return apiCall('/analytics/applications');
}

export async function getJobSearchStats(): Promise<ApiResponse<any>> {
  return apiCall('/analytics/job-search');
}
