"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, Search, Settings as SettingsIcon, BarChart3, FileText, Briefcase, Loader2 } from "lucide-react";
import { uploadResume, searchJobs } from "@/lib/api";
import { JobSearchCriteria, JobPlatform, ExperienceLevel } from "@/types";

export function Dashboard() {
  const [activeTab, setActiveTab] = useState("upload");

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Briefcase className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">JobBot</h1>
            </div>
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab("upload")}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === "upload"
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                Upload Resume
              </button>
              <button
                onClick={() => setActiveTab("search")}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === "search"
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                Job Search
              </button>
              <button
                onClick={() => setActiveTab("applications")}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === "applications"
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                Applications
              </button>
              <button
                onClick={() => setActiveTab("settings")}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeTab === "settings"
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                Settings
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeTab === "upload" && <ResumeUpload />}
          {activeTab === "search" && <JobSearch />}
          {activeTab === "applications" && <ApplicationTracker />}
          {activeTab === "settings" && <Settings />}
        </div>
      </main>
    </div>
  );
}

function ResumeUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("");
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setUploadStatus("Please select a file first");
      return;
    }

    setIsUploading(true);
    setUploadStatus("");

    try {
      const result = await uploadResume(selectedFile);

      if (result.success) {
        setUploadStatus("Resume uploaded successfully!");
        setSelectedFile(null);
        // Reset file input
        const fileInput = document.getElementById('resume-upload') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
      } else {
        setUploadStatus(result.error || "Upload failed");
      }
    } catch (error) {
      setUploadStatus("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Upload Your Resume</h2>
        <p className="mt-2 text-gray-600">
          Upload your resume to get started with automated job applications
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="h-5 w-5 mr-2" />
            Resume Upload
          </CardTitle>
          <CardDescription>
            Upload your resume in PDF format. We'll analyze it and tailor it for different job applications.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <Label htmlFor="resume-upload" className="cursor-pointer">
                <span className="mt-2 block text-sm font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : "Drop your resume here or click to browse"}
                </span>
              </Label>
              <Input
                id="resume-upload"
                type="file"
                accept=".pdf,.doc,.docx"
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>
            <p className="mt-2 text-xs text-gray-500">
              PDF, DOC, DOCX up to 10MB
            </p>
          </div>

          {uploadStatus && (
            <div className={`text-sm p-3 rounded ${
              uploadStatus.includes('success')
                ? 'bg-green-100 text-green-700'
                : 'bg-red-100 text-red-700'
            }`}>
              {uploadStatus}
            </div>
          )}

          <Button
            className="w-full"
            onClick={handleUpload}
            disabled={isUploading || !selectedFile}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              "Upload Resume"
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

function JobSearch() {
  const [searchCriteria, setSearchCriteria] = useState<JobSearchCriteria>({
    jobTitle: "",
    location: "",
    experienceLevel: "mid" as ExperienceLevel,
    minSalary: 0,
    platforms: []
  });
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);
  const [searchStatus, setSearchStatus] = useState<string>("");

  const handleInputChange = (field: keyof JobSearchCriteria, value: any) => {
    setSearchCriteria(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePlatformChange = (platform: JobPlatform, checked: boolean) => {
    setSearchCriteria(prev => ({
      ...prev,
      platforms: checked
        ? [...prev.platforms, platform]
        : prev.platforms.filter(p => p !== platform)
    }));
  };

  const handleSearch = async () => {
    if (!searchCriteria.jobTitle.trim()) {
      setSearchStatus("Please enter a job title");
      return;
    }

    if (searchCriteria.platforms.length === 0) {
      setSearchStatus("Please select at least one platform");
      return;
    }

    setIsSearching(true);
    setSearchStatus("");
    setSearchResults(null);

    try {
      const result = await searchJobs(searchCriteria);

      if (result.success) {
        setSearchResults(result.data);
        setSearchStatus(`Found ${result.data?.totalJobs || 0} jobs`);
      } else {
        setSearchStatus(result.error || "Search failed");
      }
    } catch (error) {
      setSearchStatus("Search failed. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Job Search Preferences</h2>
        <p className="mt-2 text-gray-600">
          Configure your job search criteria and select platforms to search
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Search Criteria
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="job-title">Job Title</Label>
              <Input
                id="job-title"
                placeholder="e.g. Software Engineer"
                value={searchCriteria.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="e.g. San Francisco, CA"
                value={searchCriteria.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="experience">Experience Level</Label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={searchCriteria.experienceLevel}
                onChange={(e) => handleInputChange('experienceLevel', e.target.value as ExperienceLevel)}
              >
                <option value="entry">Entry Level</option>
                <option value="mid">Mid Level</option>
                <option value="senior">Senior Level</option>
                <option value="executive">Executive</option>
              </select>
            </div>
            <div>
              <Label htmlFor="salary">Minimum Salary</Label>
              <Input
                id="salary"
                type="number"
                placeholder="e.g. 80000"
                value={searchCriteria.minSalary || ""}
                onChange={(e) => handleInputChange('minSalary', parseInt(e.target.value) || 0)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Job Platforms</CardTitle>
            <CardDescription>Select platforms to search for jobs</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="workatastartup"
                className="rounded"
                checked={searchCriteria.platforms.includes('workatastartup')}
                onChange={(e) => handlePlatformChange('workatastartup', e.target.checked)}
              />
              <Label htmlFor="workatastartup">Work at a Startup</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="linkedin" className="rounded" disabled />
              <Label htmlFor="linkedin" className="text-gray-400">LinkedIn (Coming Soon)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="indeed" className="rounded" disabled />
              <Label htmlFor="indeed" className="text-gray-400">Indeed (Coming Soon)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="glassdoor" className="rounded" disabled />
              <Label htmlFor="glassdoor" className="text-gray-400">Glassdoor (Coming Soon)</Label>
            </div>
          </CardContent>
        </Card>
      </div>

      {searchStatus && (
        <div className={`text-sm p-3 rounded ${
          searchStatus.includes('Found')
            ? 'bg-green-100 text-green-700'
            : 'bg-red-100 text-red-700'
        }`}>
          {searchStatus}
        </div>
      )}

      <Button
        size="lg"
        className="w-full"
        onClick={handleSearch}
        disabled={isSearching}
      >
        {isSearching ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Searching...
          </>
        ) : (
          "Start Job Search"
        )}
      </Button>

      {searchResults && searchResults.jobs && searchResults.jobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>Found {searchResults.totalJobs} jobs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.jobs.map((job: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{job.title}</h3>
                      <p className="text-gray-600">{job.company}</p>
                      <p className="text-sm text-gray-500">{job.location}</p>
                      {job.salary && <p className="text-sm font-medium text-green-600">{job.salary}</p>}
                    </div>
                    <Button size="sm" variant="outline">
                      Apply
                    </Button>
                  </div>
                  <p className="mt-2 text-sm text-gray-700 line-clamp-2">{job.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ApplicationTracker() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Application Tracker</h2>
        <p className="mt-2 text-gray-600">
          Track your job applications and their status
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Application Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">0</div>
              <div className="text-sm text-gray-500">Total Applications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">0</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">0</div>
              <div className="text-sm text-gray-500">Interviews</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">0</div>
              <div className="text-sm text-gray-500">Rejected</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No applications yet. Start by uploading your resume and configuring job search preferences.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function Settings() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Settings</h2>
        <p className="mt-2 text-gray-600">
          Configure your application preferences and automation settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <SettingsIcon className="h-5 w-5 mr-2" />
            Application Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Auto-apply to jobs</Label>
              <p className="text-sm text-gray-500">Automatically apply to jobs that match your criteria</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label>Email notifications</Label>
              <p className="text-sm text-gray-500">Get notified when applications are submitted</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div>
            <Label htmlFor="daily-limit">Daily application limit</Label>
            <Input id="daily-limit" type="number" placeholder="10" className="mt-1" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
