"use client";

import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, Search, Settings as SettingsIcon, BarChart3, FileText, Briefcase, Loader2, User, LogOut, Mail, Target, Zap } from "lucide-react";
import { uploadResume, searchJobs } from "@/lib/api";
import { JobSearchCriteria, JobPlatform, ExperienceLevel } from "@/types";

export function Dashboard({ session: propSession }: { session?: any }) {
  const { data: sessionData } = useSession();
  const session = propSession || sessionData;
  const [activeTab, setActiveTab] = useState("upload");
  const [emailStats, setEmailStats] = useState<any>(null);

  useEffect(() => {
    const loadEmailStats = async () => {
      try {
        const response = await fetch('/api/email/stats');
        if (response.ok) {
          const stats = await response.json();
          setEmailStats(stats);
        }
      } catch (error) {
        console.error('Failed to load email stats:', error);
      }
    };

    if (session?.user) {
      loadEmailStats();
    }
  }, [session]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/10 to-blue-600/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="bg-white/90 backdrop-blur-xl shadow-lg border-b border-white/30 sticky top-0 z-50 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-5">
            <div className="flex items-center group">
              <div className="p-3 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-2xl shadow-xl mr-4 group-hover:shadow-2xl transition-all duration-300 group-hover:scale-105">
                <Briefcase className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  JobBot AI
                </h1>
                <p className="text-sm text-gray-600 -mt-1 font-medium">Intelligent Job Application Assistant</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm rounded-2xl px-4 py-3 shadow-lg border border-white/40">
                <div className="relative">
                  <div className="p-2 bg-gradient-to-br from-emerald-100 to-green-200 rounded-xl">
                    <User className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 text-sm">
                    {session?.user?.name || 'Developer User'}
                  </p>
                  <p className="text-xs text-gray-600">
                    {session?.user?.email}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => signOut()}
                className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm border-white/40 hover:bg-white/80 transition-all duration-200 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl"
              >
                <LogOut className="h-4 w-4" />
                <span className="font-medium">Sign Out</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-t border-white/20 bg-gradient-to-r from-white/50 to-white/30 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="flex space-x-2 py-4 overflow-x-auto scrollbar-hide">
              <button
                onClick={() => setActiveTab("upload")}
                className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 flex items-center space-x-3 whitespace-nowrap transform hover:scale-105 ${
                  activeTab === "upload"
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-2xl shadow-blue-500/25 border border-white/20"
                    : "text-gray-700 hover:text-gray-900 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/40 hover:shadow-lg"
                }`}
              >
                <div className={`p-1 rounded-lg ${activeTab === "upload" ? "bg-white/20" : "bg-blue-100"}`}>
                  <Upload className={`h-4 w-4 ${activeTab === "upload" ? "text-white" : "text-blue-600"}`} />
                </div>
                <span>Upload Resume</span>
              </button>
              <button
                onClick={() => setActiveTab("search")}
                className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 flex items-center space-x-3 whitespace-nowrap transform hover:scale-105 ${
                  activeTab === "search"
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-2xl shadow-blue-500/25 border border-white/20"
                    : "text-gray-700 hover:text-gray-900 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/40 hover:shadow-lg"
                }`}
              >
                <div className={`p-1 rounded-lg ${activeTab === "search" ? "bg-white/20" : "bg-green-100"}`}>
                  <Search className={`h-4 w-4 ${activeTab === "search" ? "text-white" : "text-green-600"}`} />
                </div>
                <span>Job Search</span>
              </button>
              <button
                onClick={() => setActiveTab("applications")}
                className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 flex items-center space-x-3 whitespace-nowrap transform hover:scale-105 ${
                  activeTab === "applications"
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-2xl shadow-blue-500/25 border border-white/20"
                    : "text-gray-700 hover:text-gray-900 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/40 hover:shadow-lg"
                }`}
              >
                <div className={`p-1 rounded-lg ${activeTab === "applications" ? "bg-white/20" : "bg-orange-100"}`}>
                  <FileText className={`h-4 w-4 ${activeTab === "applications" ? "text-white" : "text-orange-600"}`} />
                </div>
                <span>Applications</span>
              </button>
              <button
                onClick={() => setActiveTab("emails")}
                className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 flex items-center space-x-3 whitespace-nowrap transform hover:scale-105 ${
                  activeTab === "emails"
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-2xl shadow-blue-500/25 border border-white/20"
                    : "text-gray-700 hover:text-gray-900 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/40 hover:shadow-lg"
                }`}
              >
                <div className={`p-1 rounded-lg ${activeTab === "emails" ? "bg-white/20" : "bg-purple-100"}`}>
                  <Mail className={`h-4 w-4 ${activeTab === "emails" ? "text-white" : "text-purple-600"}`} />
                </div>
                <span>Email Tracking</span>
              </button>
              <button
                onClick={() => setActiveTab("settings")}
                className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 flex items-center space-x-3 whitespace-nowrap transform hover:scale-105 ${
                  activeTab === "settings"
                    ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-2xl shadow-blue-500/25 border border-white/20"
                    : "text-gray-700 hover:text-gray-900 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/40 hover:shadow-lg"
                }`}
              >
                <div className={`p-1 rounded-lg ${activeTab === "settings" ? "bg-white/20" : "bg-gray-100"}`}>
                  <SettingsIcon className={`h-4 w-4 ${activeTab === "settings" ? "text-white" : "text-gray-600"}`} />
                </div>
                <span>Settings</span>
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-12 sm:px-6 lg:px-8 relative z-10">
        <div className="px-4 sm:px-0">
          <div className="transition-all duration-500 ease-in-out">
            {activeTab === "upload" && (
              <div className="animate-in slide-in-from-bottom-4 duration-500">
                <ResumeUpload />
              </div>
            )}
            {activeTab === "search" && (
              <div className="animate-in slide-in-from-bottom-4 duration-500">
                <JobSearch />
              </div>
            )}
            {activeTab === "applications" && (
              <div className="animate-in slide-in-from-bottom-4 duration-500">
                <ApplicationTracker />
              </div>
            )}
            {activeTab === "emails" && (
              <div className="animate-in slide-in-from-bottom-4 duration-500">
                <EmailTracking emailStats={emailStats} />
              </div>
            )}
            {activeTab === "settings" && (
              <div className="animate-in slide-in-from-bottom-4 duration-500">
                <Settings />
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

function ResumeUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("");
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setUploadStatus("Please select a file first");
      return;
    }

    setIsUploading(true);
    setUploadStatus("");

    try {
      const result = await uploadResume(selectedFile);

      if (result.success) {
        setUploadStatus("Resume uploaded successfully!");
        setSelectedFile(null);
        // Reset file input
        const fileInput = document.getElementById('resume-upload') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
      } else {
        setUploadStatus(result.error || "Upload failed");
      }
    } catch (error) {
      setUploadStatus("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-12">
      <div className="text-center space-y-6">
        <div className="relative">
          <h2 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-6 animate-in fade-in-50 duration-1000">
            Upload Your Resume
          </h2>
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-60"></div>
        </div>
        <p className="text-gray-700 max-w-3xl mx-auto leading-relaxed text-lg font-medium">
          Upload your resume to get started with AI-powered job applications. Our advanced system will analyze, optimize, and tailor your resume for each specific job opportunity.
        </p>
        <div className="flex justify-center space-x-8 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>AI-Powered Analysis</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-200"></div>
            <span>ATS Optimization</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-500"></div>
            <span>Job-Specific Tailoring</span>
          </div>
        </div>
      </div>

      <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-xl relative overflow-hidden group hover:shadow-3xl transition-all duration-500">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/30 to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        <CardHeader className="text-center pb-4 relative z-10">
          <CardTitle className="flex items-center justify-center text-2xl">
            <div className="p-3 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-2xl shadow-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
              <Upload className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent font-bold">
              Resume Upload Center
            </span>
          </CardTitle>
          <CardDescription className="text-gray-700 text-base mt-2">
            Upload your resume in PDF, DOC, or DOCX format. Our AI will analyze and optimize it for maximum impact.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8 p-10 relative z-10">
          <div className="relative group">
            <div className="border-3 border-dashed border-gradient-to-r from-blue-300 via-indigo-300 to-purple-300 rounded-3xl p-16 text-center bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/80 hover:from-blue-100/90 hover:via-indigo-100/70 hover:to-purple-100/90 transition-all duration-500 transform hover:scale-[1.02] cursor-pointer relative overflow-hidden">

              {/* Animated background elements */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute top-4 left-4 w-3 h-3 bg-blue-400 rounded-full animate-bounce"></div>
                <div className="absolute top-8 right-8 w-2 h-2 bg-indigo-400 rounded-full animate-bounce delay-200"></div>
                <div className="absolute bottom-6 left-8 w-2 h-2 bg-purple-400 rounded-full animate-bounce delay-500"></div>
                <div className="absolute bottom-4 right-6 w-3 h-3 bg-pink-400 rounded-full animate-bounce delay-700"></div>
              </div>

              <div className="relative z-10">
                <div className="p-6 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-3xl shadow-2xl mx-auto w-fit mb-8 group-hover:shadow-3xl group-hover:scale-110 transition-all duration-300">
                  <Upload className="h-12 w-12 text-white animate-pulse" />
                </div>

                <div className="space-y-6">
                  <Label htmlFor="resume-upload" className="cursor-pointer block">
                    <span className="text-2xl font-bold text-gray-800 hover:text-blue-600 transition-colors block mb-2">
                      {selectedFile ? (
                        <span className="text-green-600 flex items-center justify-center space-x-2">
                          <FileText className="h-6 w-6" />
                          <span>{selectedFile.name}</span>
                        </span>
                      ) : (
                        "Drop your resume here or click to browse"
                      )}
                    </span>
                    <span className="text-gray-600 text-base">
                      {selectedFile ? "File selected successfully!" : "Drag & drop or click to select your resume"}
                    </span>
                  </Label>

                  <Input
                    id="resume-upload"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    onChange={handleFileSelect}
                  />

                  <div className="space-y-4">
                    <p className="text-gray-600 font-medium">Supported formats: PDF, DOC, DOCX (up to 10MB)</p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center justify-center space-x-2 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-white/40">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="font-medium text-gray-700">AI Analysis</span>
                      </div>
                      <div className="flex items-center justify-center space-x-2 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-white/40">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-200"></div>
                        <span className="font-medium text-gray-700">ATS Optimization</span>
                      </div>
                      <div className="flex items-center justify-center space-x-2 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-white/40">
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-500"></div>
                        <span className="font-medium text-gray-700">Job Tailoring</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {uploadStatus && (
            <div className={`text-base p-6 rounded-2xl font-semibold border-2 backdrop-blur-sm transition-all duration-500 animate-in slide-in-from-top-2 ${
              uploadStatus.includes('success')
                ? 'bg-gradient-to-r from-green-50/90 to-emerald-50/90 text-green-800 border-green-300 shadow-lg shadow-green-500/20'
                : 'bg-gradient-to-r from-red-50/90 to-rose-50/90 text-red-800 border-red-300 shadow-lg shadow-red-500/20'
            }`}>
              <div className="flex items-center justify-center space-x-3">
                {uploadStatus.includes('success') ? (
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                ) : (
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                )}
                <span>{uploadStatus}</span>
              </div>
            </div>
          )}

          <Button
            className="w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white font-bold py-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-lg"
            onClick={handleUpload}
            disabled={isUploading || !selectedFile}
          >
            {isUploading ? (
              <div className="flex items-center justify-center space-x-3">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Processing Your Resume...</span>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-100"></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-200"></div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-3">
                <div className="p-1 bg-white/20 rounded-lg">
                  <Upload className="h-5 w-5" />
                </div>
                <span>Upload & Analyze Resume</span>
                <div className="p-1 bg-white/20 rounded-lg">
                  <Zap className="h-5 w-5" />
                </div>
              </div>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Features Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <Card className="border-0 bg-white/80 backdrop-blur-xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardContent className="p-8 text-center relative z-10">
            <div className="p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl w-fit mx-auto mb-6 shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">AI Analysis</h3>
            <p className="text-gray-700 leading-relaxed">Advanced artificial intelligence analyzes your resume structure, content, and keywords for maximum optimization opportunities</p>
            <div className="mt-4 flex justify-center">
              <div className="px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
                Powered by GPT-4
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-white/80 backdrop-blur-xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardContent className="p-8 text-center relative z-10">
            <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl w-fit mx-auto mb-6 shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
              <Target className="h-8 w-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors">Smart Matching</h3>
            <p className="text-gray-700 leading-relaxed">Intelligently matches your skills and experience with job requirements, automatically tailoring your resume for each application</p>
            <div className="mt-4 flex justify-center">
              <div className="px-3 py-1 bg-indigo-100 text-indigo-700 text-xs font-semibold rounded-full">
                Machine Learning
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-white/80 backdrop-blur-xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardContent className="p-8 text-center relative z-10">
            <div className="p-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl w-fit mx-auto mb-6 shadow-xl group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
              <Zap className="h-8 w-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">ATS Optimization</h3>
            <p className="text-gray-700 leading-relaxed">Ensures your resume passes through Applicant Tracking Systems with optimized formatting and keyword placement</p>
            <div className="mt-4 flex justify-center">
              <div className="px-3 py-1 bg-purple-100 text-purple-700 text-xs font-semibold rounded-full">
                99% Pass Rate
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function JobSearch() {
  const [searchCriteria, setSearchCriteria] = useState<JobSearchCriteria>({
    jobTitle: "",
    location: "",
    experienceLevel: "mid" as ExperienceLevel,
    minSalary: 0,
    platforms: []
  });
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);
  const [searchStatus, setSearchStatus] = useState<string>("");

  const handleInputChange = (field: keyof JobSearchCriteria, value: any) => {
    setSearchCriteria(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePlatformChange = (platform: JobPlatform, checked: boolean) => {
    setSearchCriteria(prev => ({
      ...prev,
      platforms: checked
        ? [...prev.platforms, platform]
        : prev.platforms.filter(p => p !== platform)
    }));
  };

  const handleSearch = async () => {
    if (!searchCriteria.jobTitle.trim()) {
      setSearchStatus("Please enter a job title");
      return;
    }

    if (searchCriteria.platforms.length === 0) {
      setSearchStatus("Please select at least one platform");
      return;
    }

    setIsSearching(true);
    setSearchStatus("");
    setSearchResults(null);

    try {
      const result = await searchJobs(searchCriteria);

      if (result.success) {
        setSearchResults(result.data);
        setSearchStatus(`Found ${result.data?.totalJobs || 0} jobs`);
      } else {
        setSearchStatus(result.error || "Search failed");
      }
    } catch (error) {
      setSearchStatus("Search failed. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4">
          Job Search Preferences
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto leading-relaxed">
          Configure your job search criteria and select platforms. Our AI will automatically find and apply to matching positions.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <CardTitle className="flex items-center justify-center text-xl">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg mr-3">
                <Search className="h-5 w-5 text-white" />
              </div>
              Search Criteria
            </CardTitle>
            <CardDescription>Define what type of jobs you're looking for</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 p-8">
            <div className="space-y-2">
              <Label htmlFor="job-title" className="text-sm font-medium text-gray-700">Job Title</Label>
              <Input
                id="job-title"
                placeholder="e.g. Software Engineer, Product Manager"
                value={searchCriteria.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location" className="text-sm font-medium text-gray-700">Location</Label>
              <Input
                id="location"
                placeholder="e.g. San Francisco, CA or Remote"
                value={searchCriteria.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="experience" className="text-sm font-medium text-gray-700">Experience Level</Label>
              <select
                className="w-full p-3 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 bg-white"
                value={searchCriteria.experienceLevel}
                onChange={(e) => handleInputChange('experienceLevel', e.target.value as ExperienceLevel)}
              >
                <option value="entry">Entry Level (0-2 years)</option>
                <option value="mid">Mid Level (3-5 years)</option>
                <option value="senior">Senior Level (6-10 years)</option>
                <option value="executive">Executive (10+ years)</option>
              </select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="salary" className="text-sm font-medium text-gray-700">Minimum Salary ($)</Label>
              <Input
                id="salary"
                type="number"
                placeholder="e.g. 80000"
                value={searchCriteria.minSalary || ""}
                onChange={(e) => handleInputChange('minSalary', parseInt(e.target.value) || 0)}
                className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <CardTitle className="flex items-center justify-center text-xl">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg mr-3">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              Job Platforms
            </CardTitle>
            <CardDescription>Select which platforms to search for jobs</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 p-8">
            <div className="space-y-4">
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="workatastartup"
                    className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    checked={searchCriteria.platforms.includes('workatastartup')}
                    onChange={(e) => handlePlatformChange('workatastartup', e.target.checked)}
                  />
                  <div className="flex-1">
                    <Label htmlFor="workatastartup" className="font-medium text-gray-900 cursor-pointer">
                      Work at a Startup
                    </Label>
                    <p className="text-sm text-gray-600">Find opportunities at innovative startups</p>
                  </div>
                  <div className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                    Active
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 opacity-60">
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="linkedin" className="w-5 h-5 rounded" disabled />
                  <div className="flex-1">
                    <Label htmlFor="linkedin" className="text-gray-400 cursor-not-allowed">LinkedIn</Label>
                    <p className="text-sm text-gray-400">Professional networking platform</p>
                  </div>
                  <div className="px-2 py-1 bg-gray-200 text-gray-500 text-xs font-medium rounded-full">
                    Coming Soon
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 opacity-60">
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="indeed" className="w-5 h-5 rounded" disabled />
                  <div className="flex-1">
                    <Label htmlFor="indeed" className="text-gray-400 cursor-not-allowed">Indeed</Label>
                    <p className="text-sm text-gray-400">World's largest job search engine</p>
                  </div>
                  <div className="px-2 py-1 bg-gray-200 text-gray-500 text-xs font-medium rounded-full">
                    Coming Soon
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 opacity-60">
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="glassdoor" className="w-5 h-5 rounded" disabled />
                  <div className="flex-1">
                    <Label htmlFor="glassdoor" className="text-gray-400 cursor-not-allowed">Glassdoor</Label>
                    <p className="text-sm text-gray-400">Company reviews and salary insights</p>
                  </div>
                  <div className="px-2 py-1 bg-gray-200 text-gray-500 text-xs font-medium rounded-full">
                    Coming Soon
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {searchStatus && (
        <div className={`text-sm p-4 rounded-xl font-medium ${
          searchStatus.includes('Found')
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border border-green-200'
            : 'bg-gradient-to-r from-red-50 to-rose-50 text-red-700 border border-red-200'
        }`}>
          {searchStatus}
        </div>
      )}

      <Button
        size="lg"
        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
        onClick={handleSearch}
        disabled={isSearching}
      >
        {isSearching ? (
          <>
            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
            Searching for Jobs...
          </>
        ) : (
          <>
            <Search className="mr-2 h-5 w-5" />
            Start AI Job Search
          </>
        )}
      </Button>

      {searchResults && searchResults.jobs && searchResults.jobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>Found {searchResults.totalJobs} jobs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.jobs.map((job: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{job.title}</h3>
                      <p className="text-gray-600">{job.company}</p>
                      <p className="text-sm text-gray-500">{job.location}</p>
                      {job.salary && <p className="text-sm font-medium text-green-600">{job.salary}</p>}
                    </div>
                    <Button size="sm" variant="outline">
                      Apply
                    </Button>
                  </div>
                  <p className="mt-2 text-sm text-gray-700 line-clamp-2">{job.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ApplicationTracker() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Application Tracker</h2>
        <p className="mt-2 text-gray-600">
          Track your job applications and their status
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Application Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">0</div>
              <div className="text-sm text-gray-500">Total Applications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">0</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">0</div>
              <div className="text-sm text-gray-500">Interviews</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">0</div>
              <div className="text-sm text-gray-500">Rejected</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No applications yet. Start by uploading your resume and configuring job search preferences.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function Settings() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Settings</h2>
        <p className="mt-2 text-gray-600">
          Configure your application preferences and automation settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <SettingsIcon className="h-5 w-5 mr-2" />
            Application Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Auto-apply to jobs</Label>
              <p className="text-sm text-gray-500">Automatically apply to jobs that match your criteria</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label>Email notifications</Label>
              <p className="text-sm text-gray-500">Get notified when applications are submitted</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div>
            <Label htmlFor="daily-limit">Daily application limit</Label>
            <Input id="daily-limit" type="number" placeholder="10" className="mt-1" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function EmailTracking({ emailStats }: { emailStats: any }) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Email Tracking Dashboard</h2>
        <p className="text-sm text-gray-600 mb-6">
          Monitor your job application emails with detailed tracking and analytics.
        </p>
      </div>

      {/* Email Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Mail className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Emails</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.total || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Open Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.openRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Click Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.clickRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Responses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.statusBreakdown?.REPLIED || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Email Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Email Status Breakdown</CardTitle>
          <CardDescription>
            Detailed breakdown of email delivery and engagement status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {emailStats ? (
            <div className="space-y-4">
              {Object.entries(emailStats.statusBreakdown || {}).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      status === 'SENT' ? 'bg-blue-500' :
                      status === 'DELIVERED' ? 'bg-green-500' :
                      status === 'OPENED' ? 'bg-purple-500' :
                      status === 'CLICKED' ? 'bg-orange-500' :
                      status === 'REPLIED' ? 'bg-emerald-500' :
                      status === 'FAILED' ? 'bg-red-500' :
                      'bg-gray-500'
                    }`} />
                    <span className="text-sm font-medium capitalize">
                      {status.toLowerCase().replace('_', ' ')}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">{count as number}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading email statistics...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Email Tracking Features */}
      <Card>
        <CardHeader>
          <CardTitle>Email Tracking Features</CardTitle>
          <CardDescription>
            Advanced email tracking capabilities for your job applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">✅ Delivery Tracking</h4>
              <p className="text-sm text-gray-600">
                Know when your emails are successfully delivered to recruiters
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">👀 Open Tracking</h4>
              <p className="text-sm text-gray-600">
                See when recruiters open and read your application emails
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">🔗 Click Tracking</h4>
              <p className="text-sm text-gray-600">
                Track when links in your emails are clicked (resume, portfolio, etc.)
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">📊 Analytics Dashboard</h4>
              <p className="text-sm text-gray-600">
                Comprehensive analytics to optimize your application strategy
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
