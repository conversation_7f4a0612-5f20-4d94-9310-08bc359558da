"use client";

import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, Search, Settings as SettingsIcon, BarChart3, FileText, Briefcase, Loader2, User, LogOut, Mail } from "lucide-react";
import { uploadResume, searchJobs } from "@/lib/api";
import { JobSearchCriteria, JobPlatform, ExperienceLevel } from "@/types";

export function Dashboard({ session: propSession }: { session?: any }) {
  const { data: sessionData } = useSession();
  const session = propSession || sessionData;
  const [activeTab, setActiveTab] = useState("upload");
  const [emailStats, setEmailStats] = useState<any>(null);

  useEffect(() => {
    const loadEmailStats = async () => {
      try {
        const response = await fetch('/api/email/stats');
        if (response.ok) {
          const stats = await response.json();
          setEmailStats(stats);
        }
      } catch (error) {
        console.error('Failed to load email stats:', error);
      }
    };

    if (session?.user) {
      loadEmailStats();
    }
  }, [session]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg mr-3">
                <Briefcase className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  JobBot
                </h1>
                <p className="text-xs text-gray-500 -mt-1">AI Job Application Assistant</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="p-1.5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900 text-sm">
                    {session?.user?.name || 'Developer User'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {session?.user?.email}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => signOut()}
                className="flex items-center space-x-2 border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-t border-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="flex space-x-1 py-2">
              <button
                onClick={() => setActiveTab("upload")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === "upload"
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <Upload className="h-4 w-4" />
                <span>Upload Resume</span>
              </button>
              <button
                onClick={() => setActiveTab("search")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === "search"
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <Search className="h-4 w-4" />
                <span>Job Search</span>
              </button>
              <button
                onClick={() => setActiveTab("applications")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === "applications"
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <FileText className="h-4 w-4" />
                <span>Applications</span>
              </button>
              <button
                onClick={() => setActiveTab("emails")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === "emails"
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <Mail className="h-4 w-4" />
                <span>Email Tracking</span>
              </button>
              <button
                onClick={() => setActiveTab("settings")}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === "settings"
                    ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </button>
            </nav>
          </div>
        </div>
      </header>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
          {activeTab === "upload" && <ResumeUpload />}
          {activeTab === "search" && <JobSearch />}
          {activeTab === "applications" && <ApplicationTracker />}
          {activeTab === "emails" && <EmailTracking emailStats={emailStats} />}
          {activeTab === "settings" && <Settings />}
        </div>
      </main>
    </div>
  );
}

function ResumeUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadStatus("");
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setUploadStatus("Please select a file first");
      return;
    }

    setIsUploading(true);
    setUploadStatus("");

    try {
      const result = await uploadResume(selectedFile);

      if (result.success) {
        setUploadStatus("Resume uploaded successfully!");
        setSelectedFile(null);
        // Reset file input
        const fileInput = document.getElementById('resume-upload') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
      } else {
        setUploadStatus(result.error || "Upload failed");
      }
    } catch (error) {
      setUploadStatus("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Upload Your Resume</h2>
        <p className="mt-2 text-gray-600">
          Upload your resume to get started with automated job applications
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="h-5 w-5 mr-2" />
            Resume Upload
          </CardTitle>
          <CardDescription>
            Upload your resume in PDF format. We'll analyze it and tailor it for different job applications.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <Label htmlFor="resume-upload" className="cursor-pointer">
                <span className="mt-2 block text-sm font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : "Drop your resume here or click to browse"}
                </span>
              </Label>
              <Input
                id="resume-upload"
                type="file"
                accept=".pdf,.doc,.docx"
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>
            <p className="mt-2 text-xs text-gray-500">
              PDF, DOC, DOCX up to 10MB
            </p>
          </div>

          {uploadStatus && (
            <div className={`text-sm p-3 rounded ${
              uploadStatus.includes('success')
                ? 'bg-green-100 text-green-700'
                : 'bg-red-100 text-red-700'
            }`}>
              {uploadStatus}
            </div>
          )}

          <Button
            className="w-full"
            onClick={handleUpload}
            disabled={isUploading || !selectedFile}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              "Upload Resume"
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

function JobSearch() {
  const [searchCriteria, setSearchCriteria] = useState<JobSearchCriteria>({
    jobTitle: "",
    location: "",
    experienceLevel: "mid" as ExperienceLevel,
    minSalary: 0,
    platforms: []
  });
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);
  const [searchStatus, setSearchStatus] = useState<string>("");

  const handleInputChange = (field: keyof JobSearchCriteria, value: any) => {
    setSearchCriteria(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePlatformChange = (platform: JobPlatform, checked: boolean) => {
    setSearchCriteria(prev => ({
      ...prev,
      platforms: checked
        ? [...prev.platforms, platform]
        : prev.platforms.filter(p => p !== platform)
    }));
  };

  const handleSearch = async () => {
    if (!searchCriteria.jobTitle.trim()) {
      setSearchStatus("Please enter a job title");
      return;
    }

    if (searchCriteria.platforms.length === 0) {
      setSearchStatus("Please select at least one platform");
      return;
    }

    setIsSearching(true);
    setSearchStatus("");
    setSearchResults(null);

    try {
      const result = await searchJobs(searchCriteria);

      if (result.success) {
        setSearchResults(result.data);
        setSearchStatus(`Found ${result.data?.totalJobs || 0} jobs`);
      } else {
        setSearchStatus(result.error || "Search failed");
      }
    } catch (error) {
      setSearchStatus("Search failed. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Job Search Preferences</h2>
        <p className="mt-2 text-gray-600">
          Configure your job search criteria and select platforms to search
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Search Criteria
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="job-title">Job Title</Label>
              <Input
                id="job-title"
                placeholder="e.g. Software Engineer"
                value={searchCriteria.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="e.g. San Francisco, CA"
                value={searchCriteria.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="experience">Experience Level</Label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={searchCriteria.experienceLevel}
                onChange={(e) => handleInputChange('experienceLevel', e.target.value as ExperienceLevel)}
              >
                <option value="entry">Entry Level</option>
                <option value="mid">Mid Level</option>
                <option value="senior">Senior Level</option>
                <option value="executive">Executive</option>
              </select>
            </div>
            <div>
              <Label htmlFor="salary">Minimum Salary</Label>
              <Input
                id="salary"
                type="number"
                placeholder="e.g. 80000"
                value={searchCriteria.minSalary || ""}
                onChange={(e) => handleInputChange('minSalary', parseInt(e.target.value) || 0)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Job Platforms</CardTitle>
            <CardDescription>Select platforms to search for jobs</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="workatastartup"
                className="rounded"
                checked={searchCriteria.platforms.includes('workatastartup')}
                onChange={(e) => handlePlatformChange('workatastartup', e.target.checked)}
              />
              <Label htmlFor="workatastartup">Work at a Startup</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="linkedin" className="rounded" disabled />
              <Label htmlFor="linkedin" className="text-gray-400">LinkedIn (Coming Soon)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="indeed" className="rounded" disabled />
              <Label htmlFor="indeed" className="text-gray-400">Indeed (Coming Soon)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="glassdoor" className="rounded" disabled />
              <Label htmlFor="glassdoor" className="text-gray-400">Glassdoor (Coming Soon)</Label>
            </div>
          </CardContent>
        </Card>
      </div>

      {searchStatus && (
        <div className={`text-sm p-3 rounded ${
          searchStatus.includes('Found')
            ? 'bg-green-100 text-green-700'
            : 'bg-red-100 text-red-700'
        }`}>
          {searchStatus}
        </div>
      )}

      <Button
        size="lg"
        className="w-full"
        onClick={handleSearch}
        disabled={isSearching}
      >
        {isSearching ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Searching...
          </>
        ) : (
          "Start Job Search"
        )}
      </Button>

      {searchResults && searchResults.jobs && searchResults.jobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>Found {searchResults.totalJobs} jobs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.jobs.map((job: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{job.title}</h3>
                      <p className="text-gray-600">{job.company}</p>
                      <p className="text-sm text-gray-500">{job.location}</p>
                      {job.salary && <p className="text-sm font-medium text-green-600">{job.salary}</p>}
                    </div>
                    <Button size="sm" variant="outline">
                      Apply
                    </Button>
                  </div>
                  <p className="mt-2 text-sm text-gray-700 line-clamp-2">{job.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ApplicationTracker() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Application Tracker</h2>
        <p className="mt-2 text-gray-600">
          Track your job applications and their status
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Application Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">0</div>
              <div className="text-sm text-gray-500">Total Applications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">0</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">0</div>
              <div className="text-sm text-gray-500">Interviews</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">0</div>
              <div className="text-sm text-gray-500">Rejected</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No applications yet. Start by uploading your resume and configuring job search preferences.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function Settings() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Settings</h2>
        <p className="mt-2 text-gray-600">
          Configure your application preferences and automation settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <SettingsIcon className="h-5 w-5 mr-2" />
            Application Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Auto-apply to jobs</Label>
              <p className="text-sm text-gray-500">Automatically apply to jobs that match your criteria</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Label>Email notifications</Label>
              <p className="text-sm text-gray-500">Get notified when applications are submitted</p>
            </div>
            <input type="checkbox" className="rounded" />
          </div>
          <div>
            <Label htmlFor="daily-limit">Daily application limit</Label>
            <Input id="daily-limit" type="number" placeholder="10" className="mt-1" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function EmailTracking({ emailStats }: { emailStats: any }) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Email Tracking Dashboard</h2>
        <p className="text-sm text-gray-600 mb-6">
          Monitor your job application emails with detailed tracking and analytics.
        </p>
      </div>

      {/* Email Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Mail className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Emails</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.total || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Open Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.openRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Click Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.clickRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Responses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {emailStats?.statusBreakdown?.REPLIED || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Email Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Email Status Breakdown</CardTitle>
          <CardDescription>
            Detailed breakdown of email delivery and engagement status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {emailStats ? (
            <div className="space-y-4">
              {Object.entries(emailStats.statusBreakdown || {}).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      status === 'SENT' ? 'bg-blue-500' :
                      status === 'DELIVERED' ? 'bg-green-500' :
                      status === 'OPENED' ? 'bg-purple-500' :
                      status === 'CLICKED' ? 'bg-orange-500' :
                      status === 'REPLIED' ? 'bg-emerald-500' :
                      status === 'FAILED' ? 'bg-red-500' :
                      'bg-gray-500'
                    }`} />
                    <span className="text-sm font-medium capitalize">
                      {status.toLowerCase().replace('_', ' ')}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">{count as number}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading email statistics...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Email Tracking Features */}
      <Card>
        <CardHeader>
          <CardTitle>Email Tracking Features</CardTitle>
          <CardDescription>
            Advanced email tracking capabilities for your job applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">✅ Delivery Tracking</h4>
              <p className="text-sm text-gray-600">
                Know when your emails are successfully delivered to recruiters
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">👀 Open Tracking</h4>
              <p className="text-sm text-gray-600">
                See when recruiters open and read your application emails
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">🔗 Click Tracking</h4>
              <p className="text-sm text-gray-600">
                Track when links in your emails are clicked (resume, portfolio, etc.)
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">📊 Analytics Dashboard</h4>
              <p className="text-sm text-gray-600">
                Comprehensive analytics to optimize your application strategy
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
