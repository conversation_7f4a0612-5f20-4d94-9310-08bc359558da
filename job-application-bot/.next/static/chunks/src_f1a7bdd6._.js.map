{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/lib/api.ts"], "sourcesContent": ["import { \n  ApiResponse, \n  JobSearchCriteria, \n  JobSearchResult, \n  JobApplication, \n  ApplicationForm \n} from '@/types';\n\nconst API_BASE_URL = process.env.NODE_ENV === 'production' \n  ? 'https://your-domain.com/api' \n  : 'http://localhost:3000/api';\n\n// Generic API call function\nasync function apiCall<T>(\n  endpoint: string, \n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return {\n        success: false,\n        error: data.error || 'An error occurred',\n      };\n    }\n\n    return {\n      success: true,\n      data,\n    };\n  } catch (error) {\n    console.error('API call error:', error);\n    return {\n      success: false,\n      error: 'Network error occurred',\n    };\n  }\n}\n\n// Resume API functions\nexport async function uploadResume(file: File): Promise<ApiResponse<any>> {\n  const formData = new FormData();\n  formData.append('resume', file);\n\n  try {\n    const response = await fetch(`${API_BASE_URL}/upload`, {\n      method: 'POST',\n      body: formData,\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return {\n        success: false,\n        error: data.error || 'Upload failed',\n      };\n    }\n\n    return {\n      success: true,\n      data,\n    };\n  } catch (error) {\n    console.error('Upload error:', error);\n    return {\n      success: false,\n      error: 'Upload failed',\n    };\n  }\n}\n\n// Job search API functions\nexport async function searchJobs(\n  criteria: JobSearchCriteria\n): Promise<ApiResponse<JobSearchResult>> {\n  return apiCall<JobSearchResult>('/jobs/search', {\n    method: 'POST',\n    body: JSON.stringify(criteria),\n  });\n}\n\n// Application API functions\nexport async function getApplications(): Promise<ApiResponse<JobApplication[]>> {\n  return apiCall<JobApplication[]>('/applications');\n}\n\nexport async function submitApplication(\n  applicationData: ApplicationForm\n): Promise<ApiResponse<JobApplication>> {\n  return apiCall<JobApplication>('/applications', {\n    method: 'POST',\n    body: JSON.stringify(applicationData),\n  });\n}\n\nexport async function updateApplicationStatus(\n  applicationId: string,\n  status: string,\n  notes?: string\n): Promise<ApiResponse<any>> {\n  return apiCall('/applications', {\n    method: 'PUT',\n    body: JSON.stringify({\n      applicationId,\n      status,\n      notes,\n    }),\n  });\n}\n\n// Settings API functions\nexport async function getUserSettings(): Promise<ApiResponse<any>> {\n  return apiCall('/settings');\n}\n\nexport async function updateUserSettings(settings: any): Promise<ApiResponse<any>> {\n  return apiCall('/settings', {\n    method: 'PUT',\n    body: JSON.stringify(settings),\n  });\n}\n\n// Analytics API functions\nexport async function getApplicationStats(): Promise<ApiResponse<any>> {\n  return apiCall('/analytics/applications');\n}\n\nexport async function getJobSearchStats(): Promise<ApiResponse<any>> {\n  return apiCall('/analytics/job-search');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAQqB;AAArB,MAAM,eAAe,6EAEjB;AAEJ,4BAA4B;AAC5B,eAAe,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;YACzD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI;YACvB;QACF;QAEA,OAAO;YACL,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,aAAa,IAAU;IAC3C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM;QACR;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI;YACvB;QACF;QAEA,OAAO;YACL,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,WACpB,QAA2B;IAE3B,OAAO,QAAyB,gBAAgB;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAGO,eAAe;IACpB,OAAO,QAA0B;AACnC;AAEO,eAAe,kBACpB,eAAgC;IAEhC,OAAO,QAAwB,iBAAiB;QAC9C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,wBACpB,aAAqB,EACrB,MAAc,EACd,KAAc;IAEd,OAAO,QAAQ,iBAAiB;QAC9B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB;YACA;YACA;QACF;IACF;AACF;AAGO,eAAe;IACpB,OAAO,QAAQ;AACjB;AAEO,eAAe,mBAAmB,QAAa;IACpD,OAAO,QAAQ,aAAa;QAC1B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAGO,eAAe;IACpB,OAAO,QAAQ;AACjB;AAEO,eAAe;IACpB,OAAO,QAAQ;AACjB", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/components/dashboard.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Upload, Search, Settings as SettingsIcon, BarChart3, FileText, Briefcase, Loader2, User, LogOut, Mail } from \"lucide-react\";\nimport { uploadResume, searchJobs } from \"@/lib/api\";\nimport { JobSearchCriteria, JobPlatform, ExperienceLevel } from \"@/types\";\n\nexport function Dashboard({ session: propSession }: { session?: any }) {\n  const { data: sessionData } = useSession();\n  const session = propSession || sessionData;\n  const [activeTab, setActiveTab] = useState(\"upload\");\n  const [emailStats, setEmailStats] = useState<any>(null);\n\n  useEffect(() => {\n    const loadEmailStats = async () => {\n      try {\n        const response = await fetch('/api/email/stats');\n        if (response.ok) {\n          const stats = await response.json();\n          setEmailStats(stats);\n        }\n      } catch (error) {\n        console.error('Failed to load email stats:', error);\n      }\n    };\n\n    if (session?.user) {\n      loadEmailStats();\n    }\n  }, [session]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg mr-3\">\n                <Briefcase className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n                  JobBot\n                </h1>\n                <p className=\"text-xs text-gray-500 -mt-1\">AI Job Application Assistant</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <div className=\"p-1.5 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg\">\n                  <User className=\"h-4 w-4 text-gray-600\" />\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"font-medium text-gray-900 text-sm\">\n                    {session?.user?.name || 'Developer User'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {session?.user?.email}\n                  </p>\n                </div>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => signOut()}\n                className=\"flex items-center space-x-2 border-gray-200 hover:bg-gray-50 transition-colors\"\n              >\n                <LogOut className=\"h-4 w-4\" />\n                <span>Sign Out</span>\n              </Button>\n            </div>\n            <nav className=\"flex space-x-8\">\n              <button\n                onClick={() => setActiveTab(\"upload\")}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === \"upload\"\n                    ? \"bg-blue-100 text-blue-700\"\n                    : \"text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                Upload Resume\n              </button>\n              <button\n                onClick={() => setActiveTab(\"search\")}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === \"search\"\n                    ? \"bg-blue-100 text-blue-700\"\n                    : \"text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                Job Search\n              </button>\n              <button\n                onClick={() => setActiveTab(\"applications\")}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === \"applications\"\n                    ? \"bg-blue-100 text-blue-700\"\n                    : \"text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                Applications\n              </button>\n              <button\n                onClick={() => setActiveTab(\"emails\")}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === \"emails\"\n                    ? \"bg-blue-100 text-blue-700\"\n                    : \"text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                Email Tracking\n              </button>\n              <button\n                onClick={() => setActiveTab(\"settings\")}\n                className={`px-3 py-2 rounded-md text-sm font-medium ${\n                  activeTab === \"settings\"\n                    ? \"bg-blue-100 text-blue-700\"\n                    : \"text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                Settings\n              </button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {activeTab === \"upload\" && <ResumeUpload />}\n          {activeTab === \"search\" && <JobSearch />}\n          {activeTab === \"applications\" && <ApplicationTracker />}\n          {activeTab === \"emails\" && <EmailTracking emailStats={emailStats} />}\n          {activeTab === \"settings\" && <Settings />}\n        </div>\n      </main>\n    </div>\n  );\n}\n\nfunction ResumeUpload() {\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadStatus, setUploadStatus] = useState<string>(\"\");\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      setUploadStatus(\"\");\n    }\n  };\n\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      setUploadStatus(\"Please select a file first\");\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadStatus(\"\");\n\n    try {\n      const result = await uploadResume(selectedFile);\n\n      if (result.success) {\n        setUploadStatus(\"Resume uploaded successfully!\");\n        setSelectedFile(null);\n        // Reset file input\n        const fileInput = document.getElementById('resume-upload') as HTMLInputElement;\n        if (fileInput) fileInput.value = '';\n      } else {\n        setUploadStatus(result.error || \"Upload failed\");\n      }\n    } catch (error) {\n      setUploadStatus(\"Upload failed. Please try again.\");\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-3xl font-bold text-gray-900\">Upload Your Resume</h2>\n        <p className=\"mt-2 text-gray-600\">\n          Upload your resume to get started with automated job applications\n        </p>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Upload className=\"h-5 w-5 mr-2\" />\n            Resume Upload\n          </CardTitle>\n          <CardDescription>\n            Upload your resume in PDF format. We'll analyze it and tailor it for different job applications.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n            <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <div className=\"mt-4\">\n              <Label htmlFor=\"resume-upload\" className=\"cursor-pointer\">\n                <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                  {selectedFile ? selectedFile.name : \"Drop your resume here or click to browse\"}\n                </span>\n              </Label>\n              <Input\n                id=\"resume-upload\"\n                type=\"file\"\n                accept=\".pdf,.doc,.docx\"\n                className=\"hidden\"\n                onChange={handleFileSelect}\n              />\n            </div>\n            <p className=\"mt-2 text-xs text-gray-500\">\n              PDF, DOC, DOCX up to 10MB\n            </p>\n          </div>\n\n          {uploadStatus && (\n            <div className={`text-sm p-3 rounded ${\n              uploadStatus.includes('success')\n                ? 'bg-green-100 text-green-700'\n                : 'bg-red-100 text-red-700'\n            }`}>\n              {uploadStatus}\n            </div>\n          )}\n\n          <Button\n            className=\"w-full\"\n            onClick={handleUpload}\n            disabled={isUploading || !selectedFile}\n          >\n            {isUploading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Uploading...\n              </>\n            ) : (\n              \"Upload Resume\"\n            )}\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nfunction JobSearch() {\n  const [searchCriteria, setSearchCriteria] = useState<JobSearchCriteria>({\n    jobTitle: \"\",\n    location: \"\",\n    experienceLevel: \"mid\" as ExperienceLevel,\n    minSalary: 0,\n    platforms: []\n  });\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState<any>(null);\n  const [searchStatus, setSearchStatus] = useState<string>(\"\");\n\n  const handleInputChange = (field: keyof JobSearchCriteria, value: any) => {\n    setSearchCriteria(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handlePlatformChange = (platform: JobPlatform, checked: boolean) => {\n    setSearchCriteria(prev => ({\n      ...prev,\n      platforms: checked\n        ? [...prev.platforms, platform]\n        : prev.platforms.filter(p => p !== platform)\n    }));\n  };\n\n  const handleSearch = async () => {\n    if (!searchCriteria.jobTitle.trim()) {\n      setSearchStatus(\"Please enter a job title\");\n      return;\n    }\n\n    if (searchCriteria.platforms.length === 0) {\n      setSearchStatus(\"Please select at least one platform\");\n      return;\n    }\n\n    setIsSearching(true);\n    setSearchStatus(\"\");\n    setSearchResults(null);\n\n    try {\n      const result = await searchJobs(searchCriteria);\n\n      if (result.success) {\n        setSearchResults(result.data);\n        setSearchStatus(`Found ${result.data?.totalJobs || 0} jobs`);\n      } else {\n        setSearchStatus(result.error || \"Search failed\");\n      }\n    } catch (error) {\n      setSearchStatus(\"Search failed. Please try again.\");\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-3xl font-bold text-gray-900\">Job Search Preferences</h2>\n        <p className=\"mt-2 text-gray-600\">\n          Configure your job search criteria and select platforms to search\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Search className=\"h-5 w-5 mr-2\" />\n              Search Criteria\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"job-title\">Job Title</Label>\n              <Input\n                id=\"job-title\"\n                placeholder=\"e.g. Software Engineer\"\n                value={searchCriteria.jobTitle}\n                onChange={(e) => handleInputChange('jobTitle', e.target.value)}\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"location\">Location</Label>\n              <Input\n                id=\"location\"\n                placeholder=\"e.g. San Francisco, CA\"\n                value={searchCriteria.location}\n                onChange={(e) => handleInputChange('location', e.target.value)}\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"experience\">Experience Level</Label>\n              <select\n                className=\"w-full p-2 border border-gray-300 rounded-md\"\n                value={searchCriteria.experienceLevel}\n                onChange={(e) => handleInputChange('experienceLevel', e.target.value as ExperienceLevel)}\n              >\n                <option value=\"entry\">Entry Level</option>\n                <option value=\"mid\">Mid Level</option>\n                <option value=\"senior\">Senior Level</option>\n                <option value=\"executive\">Executive</option>\n              </select>\n            </div>\n            <div>\n              <Label htmlFor=\"salary\">Minimum Salary</Label>\n              <Input\n                id=\"salary\"\n                type=\"number\"\n                placeholder=\"e.g. 80000\"\n                value={searchCriteria.minSalary || \"\"}\n                onChange={(e) => handleInputChange('minSalary', parseInt(e.target.value) || 0)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Job Platforms</CardTitle>\n            <CardDescription>Select platforms to search for jobs</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                id=\"workatastartup\"\n                className=\"rounded\"\n                checked={searchCriteria.platforms.includes('workatastartup')}\n                onChange={(e) => handlePlatformChange('workatastartup', e.target.checked)}\n              />\n              <Label htmlFor=\"workatastartup\">Work at a Startup</Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <input type=\"checkbox\" id=\"linkedin\" className=\"rounded\" disabled />\n              <Label htmlFor=\"linkedin\" className=\"text-gray-400\">LinkedIn (Coming Soon)</Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <input type=\"checkbox\" id=\"indeed\" className=\"rounded\" disabled />\n              <Label htmlFor=\"indeed\" className=\"text-gray-400\">Indeed (Coming Soon)</Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <input type=\"checkbox\" id=\"glassdoor\" className=\"rounded\" disabled />\n              <Label htmlFor=\"glassdoor\" className=\"text-gray-400\">Glassdoor (Coming Soon)</Label>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {searchStatus && (\n        <div className={`text-sm p-3 rounded ${\n          searchStatus.includes('Found')\n            ? 'bg-green-100 text-green-700'\n            : 'bg-red-100 text-red-700'\n        }`}>\n          {searchStatus}\n        </div>\n      )}\n\n      <Button\n        size=\"lg\"\n        className=\"w-full\"\n        onClick={handleSearch}\n        disabled={isSearching}\n      >\n        {isSearching ? (\n          <>\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n            Searching...\n          </>\n        ) : (\n          \"Start Job Search\"\n        )}\n      </Button>\n\n      {searchResults && searchResults.jobs && searchResults.jobs.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Search Results</CardTitle>\n            <CardDescription>Found {searchResults.totalJobs} jobs</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {searchResults.jobs.map((job: any, index: number) => (\n                <div key={index} className=\"border rounded-lg p-4\">\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <h3 className=\"font-semibold text-lg\">{job.title}</h3>\n                      <p className=\"text-gray-600\">{job.company}</p>\n                      <p className=\"text-sm text-gray-500\">{job.location}</p>\n                      {job.salary && <p className=\"text-sm font-medium text-green-600\">{job.salary}</p>}\n                    </div>\n                    <Button size=\"sm\" variant=\"outline\">\n                      Apply\n                    </Button>\n                  </div>\n                  <p className=\"mt-2 text-sm text-gray-700 line-clamp-2\">{job.description}</p>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n\nfunction ApplicationTracker() {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-3xl font-bold text-gray-900\">Application Tracker</h2>\n        <p className=\"mt-2 text-gray-600\">\n          Track your job applications and their status\n        </p>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <BarChart3 className=\"h-5 w-5 mr-2\" />\n            Application Statistics\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-blue-600\">0</div>\n              <div className=\"text-sm text-gray-500\">Total Applications</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-yellow-600\">0</div>\n              <div className=\"text-sm text-gray-500\">Pending</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-green-600\">0</div>\n              <div className=\"text-sm text-gray-500\">Interviews</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-red-600\">0</div>\n              <div className=\"text-sm text-gray-500\">Rejected</div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Applications</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-8 text-gray-500\">\n            No applications yet. Start by uploading your resume and configuring job search preferences.\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nfunction Settings() {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-3xl font-bold text-gray-900\">Settings</h2>\n        <p className=\"mt-2 text-gray-600\">\n          Configure your application preferences and automation settings\n        </p>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <SettingsIcon className=\"h-5 w-5 mr-2\" />\n            Application Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Label>Auto-apply to jobs</Label>\n              <p className=\"text-sm text-gray-500\">Automatically apply to jobs that match your criteria</p>\n            </div>\n            <input type=\"checkbox\" className=\"rounded\" />\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Label>Email notifications</Label>\n              <p className=\"text-sm text-gray-500\">Get notified when applications are submitted</p>\n            </div>\n            <input type=\"checkbox\" className=\"rounded\" />\n          </div>\n          <div>\n            <Label htmlFor=\"daily-limit\">Daily application limit</Label>\n            <Input id=\"daily-limit\" type=\"number\" placeholder=\"10\" className=\"mt-1\" />\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nfunction EmailTracking({ emailStats }: { emailStats: any }) {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Email Tracking Dashboard</h2>\n        <p className=\"text-sm text-gray-600 mb-6\">\n          Monitor your job application emails with detailed tracking and analytics.\n        </p>\n      </div>\n\n      {/* Email Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <Mail className=\"h-8 w-8 text-blue-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Emails</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {emailStats?.total || 0}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <BarChart3 className=\"h-8 w-8 text-green-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Open Rate</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {emailStats?.openRate?.toFixed(1) || 0}%\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <Search className=\"h-8 w-8 text-purple-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Click Rate</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {emailStats?.clickRate?.toFixed(1) || 0}%\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <FileText className=\"h-8 w-8 text-orange-600\" />\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Responses</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {emailStats?.statusBreakdown?.REPLIED || 0}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Email Status Breakdown */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Email Status Breakdown</CardTitle>\n          <CardDescription>\n            Detailed breakdown of email delivery and engagement status\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {emailStats ? (\n            <div className=\"space-y-4\">\n              {Object.entries(emailStats.statusBreakdown || {}).map(([status, count]) => (\n                <div key={status} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className={`w-3 h-3 rounded-full ${\n                      status === 'SENT' ? 'bg-blue-500' :\n                      status === 'DELIVERED' ? 'bg-green-500' :\n                      status === 'OPENED' ? 'bg-purple-500' :\n                      status === 'CLICKED' ? 'bg-orange-500' :\n                      status === 'REPLIED' ? 'bg-emerald-500' :\n                      status === 'FAILED' ? 'bg-red-500' :\n                      'bg-gray-500'\n                    }`} />\n                    <span className=\"text-sm font-medium capitalize\">\n                      {status.toLowerCase().replace('_', ' ')}\n                    </span>\n                  </div>\n                  <span className=\"text-sm text-gray-600\">{count as number}</span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center py-8\">\n              <Loader2 className=\"h-6 w-6 animate-spin text-gray-400\" />\n              <span className=\"ml-2 text-gray-500\">Loading email statistics...</span>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Email Tracking Features */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Email Tracking Features</CardTitle>\n          <CardDescription>\n            Advanced email tracking capabilities for your job applications\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">✅ Delivery Tracking</h4>\n              <p className=\"text-sm text-gray-600\">\n                Know when your emails are successfully delivered to recruiters\n              </p>\n            </div>\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">👀 Open Tracking</h4>\n              <p className=\"text-sm text-gray-600\">\n                See when recruiters open and read your application emails\n              </p>\n            </div>\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">🔗 Click Tracking</h4>\n              <p className=\"text-sm text-gray-600\">\n                Track when links in your emails are clicked (resume, portfolio, etc.)\n              </p>\n            </div>\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-gray-900\">📊 Analytics Dashboard</h4>\n              <p className=\"text-sm text-gray-600\">\n                Comprehensive analytics to optimize your application strategy\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAYO,SAAS,UAAU,EAAE,SAAS,WAAW,EAAqB;;IACnE,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACvC,MAAM,UAAU,eAAe;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;sDAAiB;oBACrB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,QAAQ,MAAM,SAAS,IAAI;4BACjC,cAAc;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;gBACF;;YAEA,IAAI,SAAS,MAAM;gBACjB;YACF;QACF;8BAAG;QAAC;KAAQ;IAEZ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+F;;;;;;0DAG7G,6LAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,SAAS,MAAM,QAAQ;;;;;;kEAE1B,6LAAC;wDAAE,WAAU;kEACV,SAAS,MAAM;;;;;;;;;;;;;;;;;;kDAItB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;wCACrB,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,8BACA,qCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,8BACA,qCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,iBACV,8BACA,qCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,8BACA,qCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,8BACA,qCACJ;kDACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,0BAAY,6LAAC;;;;;wBAC3B,cAAc,0BAAY,6LAAC;;;;;wBAC3B,cAAc,gCAAkB,6LAAC;;;;;wBACjC,cAAc,0BAAY,6LAAC;4BAAc,YAAY;;;;;;wBACrD,cAAc,4BAAc,6LAAC;;;;;;;;;;;;;;;;;;;;;;AAKxC;GArIgB;;QACgB,iJAAA,CAAA,aAAU;;;KAD1B;AAuIhB,SAAS;;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB,gBAAgB;YAChB;QACF;QAEA,eAAe;QACf,gBAAgB;QAEhB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB;gBAChB,gBAAgB;gBAChB,mBAAmB;gBACnB,MAAM,YAAY,SAAS,cAAc,CAAC;gBAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;YACnC,OAAO;gBACL,gBAAgB,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;0DACvC,cAAA,6LAAC;oDAAK,WAAU;8DACb,eAAe,aAAa,IAAI,GAAG;;;;;;;;;;;0DAGxC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,WAAU;gDACV,UAAU;;;;;;;;;;;;kDAGd,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;4BAK3C,8BACC,6LAAC;gCAAI,WAAW,CAAC,oBAAoB,EACnC,aAAa,QAAQ,CAAC,aAClB,gCACA,2BACJ;0CACC;;;;;;0CAIL,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS;gCACT,UAAU,eAAe,CAAC;0CAEzB,4BACC;;sDACE,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mDAInD;;;;;;;;;;;;;;;;;;;;;;;;AAOd;IA9GS;MAAA;AAgHT,SAAS;;IACP,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QACtE,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,WAAW;QACX,WAAW,EAAE;IACf;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,oBAAoB,CAAC,OAAgC;QACzD,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,UAAuB;QACnD,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,WAAW,UACP;uBAAI,KAAK,SAAS;oBAAE;iBAAS,GAC7B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YACvC,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,eAAe,QAAQ,CAAC,IAAI,IAAI;YACnC,gBAAgB;YAChB;QACF;QAEA,IAAI,eAAe,SAAS,CAAC,MAAM,KAAK,GAAG;YACzC,gBAAgB;YAChB;QACF;QAEA,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QAEjB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;YAEhC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI;gBAC5B,gBAAgB,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC;YAC7D,OAAO;gBACL,gBAAgB,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,eAAe,QAAQ;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAGjE,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,eAAe,QAAQ;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAGjE,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6LAAC;gDACC,WAAU;gDACV,OAAO,eAAe,eAAe;gDACrC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;;kEAEpE,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;;;;;;;;;;;;;kDAG9B,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO,eAAe,SAAS,IAAI;gDACnC,UAAU,CAAC,IAAM,kBAAkB,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMpF,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,SAAS,eAAe,SAAS,CAAC,QAAQ,CAAC;gDAC3C,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,OAAO;;;;;;0DAE1E,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,MAAK;gDAAW,IAAG;gDAAW,WAAU;gDAAU,QAAQ;;;;;;0DACjE,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAW,WAAU;0DAAgB;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,MAAK;gDAAW,IAAG;gDAAS,WAAU;gDAAU,QAAQ;;;;;;0DAC/D,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAS,WAAU;0DAAgB;;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,MAAK;gDAAW,IAAG;gDAAY,WAAU;gDAAU,QAAQ;;;;;;0DAClE,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM5D,8BACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,aAAa,QAAQ,CAAC,WAClB,gCACA,2BACJ;0BACC;;;;;;0BAIL,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;0BAET,4BACC;;sCACE,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;mCAInD;;;;;;YAIH,iBAAiB,cAAc,IAAI,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,mBAClE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;;oCAAC;oCAAO,cAAc,SAAS;oCAAC;;;;;;;;;;;;;kCAElD,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,sBACjC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAyB,IAAI,KAAK;;;;;;sEAChD,6LAAC;4DAAE,WAAU;sEAAiB,IAAI,OAAO;;;;;;sEACzC,6LAAC;4DAAE,WAAU;sEAAyB,IAAI,QAAQ;;;;;;wDACjD,IAAI,MAAM,kBAAI,6LAAC;4DAAE,WAAU;sEAAsC,IAAI,MAAM;;;;;;;;;;;;8DAE9E,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;8DAAU;;;;;;;;;;;;sDAItC,6LAAC;4CAAE,WAAU;sDAA2C,IAAI,WAAW;;;;;;;mCAZ/D;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB1B;IAjNS;MAAA;AAmNT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM/C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAO1D;MAnDS;AAqDT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAY;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI7C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAM,MAAK;wCAAW,WAAU;;;;;;;;;;;;0CAEnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAM,MAAK;wCAAW,WAAU;;;;;;;;;;;;0CAEnC,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,oIAAA,CAAA,QAAK;wCAAC,IAAG;wCAAc,MAAK;wCAAS,aAAY;wCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7E;MAxCS;AA0CT,SAAS,cAAc,EAAE,UAAU,EAAuB;IACxD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDACV,YAAY,UAAU,QAAQ,MAAM;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDACV,YAAY,WAAW,QAAQ,MAAM;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,YAAY,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACT,2BACC,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,WAAW,eAAe,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBACpE,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,WAAW,SAAS,gBACpB,WAAW,cAAc,iBACzB,WAAW,WAAW,kBACtB,WAAW,YAAY,kBACvB,WAAW,YAAY,mBACvB,WAAW,WAAW,eACtB,eACA;;;;;;8DACF,6LAAC;oDAAK,WAAU;8DACb,OAAO,WAAW,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAGvC,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;mCAfjC;;;;;;;;;iDAoBd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;MApJS", "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport { Dashboard } from \"@/components/dashboard\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Briefcase, Loader2 } from \"lucide-react\";\nimport { signIn } from \"next-auth/react\";\n\nexport default function Home() {\n  const { data: session, status } = useSession();\n\n  if (status === \"loading\") {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4\" />\n          <p className=\"text-gray-600 font-medium\">Loading JobBot...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Bypass login in development mode\n  if (!session && process.env.NODE_ENV === 'development') {\n    // Create a mock session for development\n    const mockSession = {\n      user: {\n        id: 'dev-user',\n        email: '<EMAIL>',\n        name: 'Developer User',\n        image: null\n      }\n    };\n    return <Dashboard session={mockSession} />;\n  }\n\n  if (!session) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\">\n        <Card className=\"w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm\">\n          <CardHeader className=\"text-center pb-2\">\n            <div className=\"flex justify-center mb-6\">\n              <div className=\"p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg\">\n                <Briefcase className=\"h-8 w-8 text-white\" />\n              </div>\n            </div>\n            <CardTitle className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n              Welcome to JobBot\n            </CardTitle>\n            <CardDescription className=\"text-gray-600 mt-2 leading-relaxed\">\n              Automate your job applications with AI-powered resume tailoring and comprehensive email tracking\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"pt-4\">\n            <Button\n              onClick={() => signIn()}\n              className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200\"\n            >\n              Sign in to get started\n            </Button>\n            <p className=\"text-xs text-gray-500 text-center mt-4\">\n              Secure authentication powered by NextAuth.js\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return <Dashboard />;\n}\n"], "names": [], "mappings": ";;;AAwBkB;;AAtBlB;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;IAIjD;IAEA,mCAAmC;IACnC,IAAI,CAAC,WAAW,oDAAyB,eAAe;QACtD,wCAAwC;QACxC,MAAM,cAAc;YAClB,MAAM;gBACJ,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;QACF;QACA,qBAAO,6LAAC,kIAAA,CAAA,YAAS;YAAC,SAAS;;;;;;IAC7B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAgG;;;;;;0CAGrH,6LAAC,mIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAqC;;;;;;;;;;;;kCAIlE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;gCACpB,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;;IAOhE;IAEA,qBAAO,6LAAC,kIAAA,CAAA,YAAS;;;;;AACnB;GA9DwB;;QACY,iJAAA,CAAA,aAAU;;;KADtB", "debugId": null}}]}