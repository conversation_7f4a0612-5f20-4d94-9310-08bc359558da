const CHUNK_PUBLIC_PATH = "server/app/api/email/stats/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_10f5c48c._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_f79bd59c._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_nodemailer_a9f338b9._.js");
runtime.loadChunk("server/chunks/node_modules_b69eec1e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__925d14ca._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/email/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/email/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/email/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
