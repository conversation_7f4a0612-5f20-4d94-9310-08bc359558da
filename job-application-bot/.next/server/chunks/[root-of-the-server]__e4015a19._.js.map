{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/api/jobs/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { jobTitle, location, experienceLevel, minSalary, platforms } = body;\n\n    // Validate required fields\n    if (!jobTitle || !platforms || platforms.length === 0) {\n      return NextResponse.json(\n        { error: 'Job title and at least one platform are required' },\n        { status: 400 }\n      );\n    }\n\n    // TODO: Implement job search logic for each platform\n    const searchResults = [];\n\n    // Work at a Startup search\n    if (platforms.includes('workatastartup')) {\n      try {\n        const workAtStartupJobs = await searchWorkAtStartup({\n          jobTitle,\n          location,\n          experienceLevel,\n          minSalary\n        });\n        searchResults.push(...workAtStartupJobs);\n      } catch (error) {\n        console.error('Work at a Startup search error:', error);\n      }\n    }\n\n    // TODO: Add other platform searches (LinkedIn, Indeed, etc.)\n\n    return NextResponse.json({\n      message: 'Job search completed',\n      totalJobs: searchResults.length,\n      jobs: searchResults\n    });\n\n  } catch (error) {\n    console.error('Job search error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function searchWorkAtStartup(criteria: {\n  jobTitle: string;\n  location?: string;\n  experienceLevel?: string;\n  minSalary?: number;\n}) {\n  // TODO: Implement actual scraping logic for Work at a Startup\n  // This is a placeholder that returns mock data\n  \n  const mockJobs = [\n    {\n      id: '1',\n      title: 'Software Engineer',\n      company: 'TechStartup Inc.',\n      location: 'San Francisco, CA',\n      salary: '$120,000 - $150,000',\n      description: 'Join our growing team as a Software Engineer...',\n      url: 'https://www.workatastartup.com/jobs/12345',\n      platform: 'workatastartup',\n      postedDate: new Date().toISOString()\n    },\n    {\n      id: '2',\n      title: 'Frontend Developer',\n      company: 'InnovateCorp',\n      location: 'Remote',\n      salary: '$100,000 - $130,000',\n      description: 'We are looking for a talented Frontend Developer...',\n      url: 'https://www.workatastartup.com/jobs/12346',\n      platform: 'workatastartup',\n      postedDate: new Date().toISOString()\n    }\n  ];\n\n  // Filter based on criteria\n  return mockJobs.filter(job => {\n    if (criteria.jobTitle && !job.title.toLowerCase().includes(criteria.jobTitle.toLowerCase())) {\n      return false;\n    }\n    // Add more filtering logic as needed\n    return true;\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;QAEtE,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,gBAAgB,EAAE;QAExB,2BAA2B;QAC3B,IAAI,UAAU,QAAQ,CAAC,mBAAmB;YACxC,IAAI;gBACF,MAAM,oBAAoB,MAAM,oBAAoB;oBAClD;oBACA;oBACA;oBACA;gBACF;gBACA,cAAc,IAAI,IAAI;YACxB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;QAEA,6DAA6D;QAE7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,WAAW,cAAc,MAAM;YAC/B,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,oBAAoB,QAKlC;IACC,8DAA8D;IAC9D,+CAA+C;IAE/C,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;YACb,KAAK;YACL,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;YACb,KAAK;YACL,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;KACD;IAED,2BAA2B;IAC3B,OAAO,SAAS,MAAM,CAAC,CAAA;QACrB,IAAI,SAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,QAAQ,CAAC,WAAW,KAAK;YAC3F,OAAO;QACT;QACA,qCAAqC;QACrC,OAAO;IACT;AACF", "debugId": null}}]}