{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/lib/email-tracking.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport nodemailer from 'nodemailer';\nimport { randomUUID } from 'crypto';\n\nexport interface EmailTrackingData {\n  userId: string;\n  applicationId?: string;\n  emailType: 'APPLICATION' | 'FOLLOW_UP' | 'THANK_YOU' | 'INTERVIEW_CONFIRMATION' | 'OFFER_RESPONSE' | 'WITHDRAWAL' | 'OTHER';\n  recipient: string;\n  subject: string;\n  content: string;\n  metadata?: any;\n}\n\nexport class EmailTracker {\n  private transporter: nodemailer.Transporter;\n\n  constructor() {\n    this.transporter = nodemailer.createTransporter({\n      host: process.env.EMAIL_SERVER_HOST,\n      port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),\n      secure: false,\n      auth: {\n        user: process.env.EMAIL_SERVER_USER,\n        pass: process.env.EMAIL_SERVER_PASSWORD,\n      },\n    });\n  }\n\n  async sendTrackedEmail(emailData: EmailTrackingData): Promise<string> {\n    const trackingPixelId = randomUUID();\n    const trackingPixelUrl = `${process.env.NEXTAUTH_URL}/api/email/track/${trackingPixelId}`;\n    \n    // Add tracking pixel to email content\n    const trackedContent = this.addTrackingPixel(emailData.content, trackingPixelUrl);\n    \n    // Add click tracking to links\n    const contentWithClickTracking = this.addClickTracking(trackedContent, trackingPixelId);\n\n    try {\n      // Send email\n      const info = await this.transporter.sendMail({\n        from: process.env.EMAIL_FROM,\n        to: emailData.recipient,\n        subject: emailData.subject,\n        html: contentWithClickTracking,\n      });\n\n      // Store tracking record in database\n      const emailTracking = await prisma.emailTracking.create({\n        data: {\n          userId: emailData.userId,\n          applicationId: emailData.applicationId,\n          emailType: emailData.emailType,\n          recipient: emailData.recipient,\n          subject: emailData.subject,\n          content: emailData.content,\n          trackingPixelId,\n          status: 'SENT',\n          metadata: {\n            messageId: info.messageId,\n            ...emailData.metadata,\n          },\n        },\n      });\n\n      return emailTracking.id;\n    } catch (error) {\n      console.error('Failed to send tracked email:', error);\n      \n      // Still create tracking record with failed status\n      await prisma.emailTracking.create({\n        data: {\n          userId: emailData.userId,\n          applicationId: emailData.applicationId,\n          emailType: emailData.emailType,\n          recipient: emailData.recipient,\n          subject: emailData.subject,\n          content: emailData.content,\n          trackingPixelId,\n          status: 'FAILED',\n          metadata: {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            ...emailData.metadata,\n          },\n        },\n      });\n\n      throw error;\n    }\n  }\n\n  private addTrackingPixel(content: string, trackingUrl: string): string {\n    const trackingPixel = `<img src=\"${trackingUrl}\" width=\"1\" height=\"1\" style=\"display:none;\" alt=\"\" />`;\n    \n    // Try to add before closing body tag, otherwise append to end\n    if (content.includes('</body>')) {\n      return content.replace('</body>', `${trackingPixel}</body>`);\n    } else {\n      return content + trackingPixel;\n    }\n  }\n\n  private addClickTracking(content: string, trackingPixelId: string): string {\n    // Replace all links with tracked versions\n    const linkRegex = /<a\\s+(?:[^>]*?\\s+)?href=([\"'])(.*?)\\1/gi;\n    \n    return content.replace(linkRegex, (match, quote, url) => {\n      const trackedUrl = `${process.env.NEXTAUTH_URL}/api/email/click/${trackingPixelId}?url=${encodeURIComponent(url)}`;\n      return match.replace(url, trackedUrl);\n    });\n  }\n\n  async trackEmailOpen(trackingPixelId: string): Promise<void> {\n    await prisma.emailTracking.updateMany({\n      where: { trackingPixelId },\n      data: { \n        openedAt: new Date(),\n        status: 'OPENED'\n      },\n    });\n  }\n\n  async trackEmailClick(trackingPixelId: string): Promise<void> {\n    await prisma.emailTracking.updateMany({\n      where: { trackingPixelId },\n      data: { \n        clickedAt: new Date(),\n        status: 'CLICKED'\n      },\n    });\n  }\n\n  async getEmailStats(userId: string, applicationId?: string) {\n    const where = applicationId \n      ? { userId, applicationId }\n      : { userId };\n\n    const stats = await prisma.emailTracking.groupBy({\n      by: ['status'],\n      where,\n      _count: {\n        status: true,\n      },\n    });\n\n    const totalEmails = await prisma.emailTracking.count({ where });\n    const openedEmails = await prisma.emailTracking.count({\n      where: { ...where, openedAt: { not: null } },\n    });\n    const clickedEmails = await prisma.emailTracking.count({\n      where: { ...where, clickedAt: { not: null } },\n    });\n\n    return {\n      total: totalEmails,\n      opened: openedEmails,\n      clicked: clickedEmails,\n      openRate: totalEmails > 0 ? (openedEmails / totalEmails) * 100 : 0,\n      clickRate: totalEmails > 0 ? (clickedEmails / totalEmails) * 100 : 0,\n      statusBreakdown: stats.reduce((acc, stat) => {\n        acc[stat.status] = stat._count.status;\n        return acc;\n      }, {} as Record<string, number>),\n    };\n  }\n}\n\nexport const emailTracker = new EmailTracker();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAYO,MAAM;IACH,YAAoC;IAE5C,aAAc;QACZ,IAAI,CAAC,WAAW,GAAG,iJAAA,CAAA,UAAU,CAAC,iBAAiB,CAAC;YAC9C,MAAM,QAAQ,GAAG,CAAC,iBAAiB;YACnC,MAAM,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;YAChD,QAAQ;YACR,MAAM;gBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB;gBACnC,MAAM,QAAQ,GAAG,CAAC,qBAAqB;YACzC;QACF;IACF;IAEA,MAAM,iBAAiB,SAA4B,EAAmB;QACpE,MAAM,kBAAkB,CAAA,GAAA,qGAAA,CAAA,aAAU,AAAD;QACjC,MAAM,mBAAmB,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,iBAAiB,EAAE,iBAAiB;QAEzF,sCAAsC;QACtC,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU,OAAO,EAAE;QAEhE,8BAA8B;QAC9B,MAAM,2BAA2B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;QAEvE,IAAI;YACF,aAAa;YACb,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,UAAU;gBAC5B,IAAI,UAAU,SAAS;gBACvB,SAAS,UAAU,OAAO;gBAC1B,MAAM;YACR;YAEA,oCAAoC;YACpC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtD,MAAM;oBACJ,QAAQ,UAAU,MAAM;oBACxB,eAAe,UAAU,aAAa;oBACtC,WAAW,UAAU,SAAS;oBAC9B,WAAW,UAAU,SAAS;oBAC9B,SAAS,UAAU,OAAO;oBAC1B,SAAS,UAAU,OAAO;oBAC1B;oBACA,QAAQ;oBACR,UAAU;wBACR,WAAW,KAAK,SAAS;wBACzB,GAAG,UAAU,QAAQ;oBACvB;gBACF;YACF;YAEA,OAAO,cAAc,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAE/C,kDAAkD;YAClD,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChC,MAAM;oBACJ,QAAQ,UAAU,MAAM;oBACxB,eAAe,UAAU,aAAa;oBACtC,WAAW,UAAU,SAAS;oBAC9B,WAAW,UAAU,SAAS;oBAC9B,SAAS,UAAU,OAAO;oBAC1B,SAAS,UAAU,OAAO;oBAC1B;oBACA,QAAQ;oBACR,UAAU;wBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAChD,GAAG,UAAU,QAAQ;oBACvB;gBACF;YACF;YAEA,MAAM;QACR;IACF;IAEQ,iBAAiB,OAAe,EAAE,WAAmB,EAAU;QACrE,MAAM,gBAAgB,CAAC,UAAU,EAAE,YAAY,sDAAsD,CAAC;QAEtG,8DAA8D;QAC9D,IAAI,QAAQ,QAAQ,CAAC,YAAY;YAC/B,OAAO,QAAQ,OAAO,CAAC,WAAW,GAAG,cAAc,OAAO,CAAC;QAC7D,OAAO;YACL,OAAO,UAAU;QACnB;IACF;IAEQ,iBAAiB,OAAe,EAAE,eAAuB,EAAU;QACzE,0CAA0C;QAC1C,MAAM,YAAY;QAElB,OAAO,QAAQ,OAAO,CAAC,WAAW,CAAC,OAAO,OAAO;YAC/C,MAAM,aAAa,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,iBAAiB,EAAE,gBAAgB,KAAK,EAAE,mBAAmB,MAAM;YAClH,OAAO,MAAM,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,eAAe,eAAuB,EAAiB;QAC3D,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE;YAAgB;YACzB,MAAM;gBACJ,UAAU,IAAI;gBACd,QAAQ;YACV;QACF;IACF;IAEA,MAAM,gBAAgB,eAAuB,EAAiB;QAC5D,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE;YAAgB;YACzB,MAAM;gBACJ,WAAW,IAAI;gBACf,QAAQ;YACV;QACF;IACF;IAEA,MAAM,cAAc,MAAc,EAAE,aAAsB,EAAE;QAC1D,MAAM,QAAQ,gBACV;YAAE;YAAQ;QAAc,IACxB;YAAE;QAAO;QAEb,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,OAAO,CAAC;YAC/C,IAAI;gBAAC;aAAS;YACd;YACA,QAAQ;gBACN,QAAQ;YACV;QACF;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC;YAAE;QAAM;QAC7D,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC;YACpD,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU;oBAAE,KAAK;gBAAK;YAAE;QAC7C;QACA,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC;YACrD,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW;oBAAE,KAAK;gBAAK;YAAE;QAC9C;QAEA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU,cAAc,IAAI,AAAC,eAAe,cAAe,MAAM;YACjE,WAAW,cAAc,IAAI,AAAC,gBAAgB,cAAe,MAAM;YACnE,iBAAiB,MAAM,MAAM,CAAC,CAAC,KAAK;gBAClC,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,MAAM;gBACrC,OAAO;YACT,GAAG,CAAC;QACN;IACF;AACF;AAEO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/api/email/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { emailTracker } from '@/lib/email-tracking';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession();\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      );\n    }\n    \n    const { searchParams } = new URL(request.url);\n    const applicationId = searchParams.get('applicationId');\n    \n    const stats = await emailTracker.getEmailStats(\n      session.user.id,\n      applicationId || undefined\n    );\n    \n    return NextResponse.json(stats);\n  } catch (error) {\n    console.error('Email stats error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;QAErC,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,MAAM,QAAQ,MAAM,iIAAA,CAAA,eAAY,CAAC,aAAa,CAC5C,QAAQ,IAAI,CAAC,EAAE,EACf,iBAAiB;QAGnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}