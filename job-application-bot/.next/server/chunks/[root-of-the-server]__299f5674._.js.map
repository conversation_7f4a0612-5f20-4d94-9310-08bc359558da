{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('resume') as File;\n    \n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file uploaded' },\n        { status: 400 }\n      );\n    }\n\n    // Validate file type\n    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!allowedTypes.includes(file.type)) {\n      return NextResponse.json(\n        { error: 'Invalid file type. Please upload PDF, DOC, or DOCX files only.' },\n        { status: 400 }\n      );\n    }\n\n    // Validate file size (10MB limit)\n    const maxSize = 10 * 1024 * 1024; // 10MB in bytes\n    if (file.size > maxSize) {\n      return NextResponse.json(\n        { error: 'File too large. Maximum size is 10MB.' },\n        { status: 400 }\n      );\n    }\n\n    // Convert file to buffer\n    const bytes = await file.arrayBuffer();\n    const buffer = Buffer.from(bytes);\n\n    // TODO: Save file to storage (Supabase Storage)\n    // TODO: Extract text from resume using AI\n    // TODO: Store resume data in database\n\n    // For now, return success response\n    return NextResponse.json({\n      message: 'Resume uploaded successfully',\n      filename: file.name,\n      size: file.size,\n      type: file.type\n    });\n\n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,eAAe;YAAC;YAAmB;YAAsB;SAA0E;QACzI,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiE,GAC1E;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,MAAM,UAAU,KAAK,OAAO,MAAM,gBAAgB;QAClD,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,gDAAgD;QAChD,0CAA0C;QAC1C,sCAAsC;QAEtC,mCAAmC;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,KAAK,IAAI;YACnB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}