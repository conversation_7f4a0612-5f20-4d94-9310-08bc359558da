module.exports = {

"[project]/.next-internal/server/app/api/jobs/search/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/jobs/search/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const body = await request.json();
        const { jobTitle, location, experienceLevel, minSalary, platforms } = body;
        // Validate required fields
        if (!jobTitle || !platforms || platforms.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Job title and at least one platform are required'
            }, {
                status: 400
            });
        }
        // TODO: Implement job search logic for each platform
        const searchResults = [];
        // Work at a Startup search
        if (platforms.includes('workatastartup')) {
            try {
                const workAtStartupJobs = await searchWorkAtStartup({
                    jobTitle,
                    location,
                    experienceLevel,
                    minSalary
                });
                searchResults.push(...workAtStartupJobs);
            } catch (error) {
                console.error('Work at a Startup search error:', error);
            }
        }
        // TODO: Add other platform searches (LinkedIn, Indeed, etc.)
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Job search completed',
            totalJobs: searchResults.length,
            jobs: searchResults
        });
    } catch (error) {
        console.error('Job search error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function searchWorkAtStartup(criteria) {
    // TODO: Implement actual scraping logic for Work at a Startup
    // This is a placeholder that returns mock data
    const mockJobs = [
        {
            id: '1',
            title: 'Software Engineer',
            company: 'TechStartup Inc.',
            location: 'San Francisco, CA',
            salary: '$120,000 - $150,000',
            description: 'Join our growing team as a Software Engineer...',
            url: 'https://www.workatastartup.com/jobs/12345',
            platform: 'workatastartup',
            postedDate: new Date().toISOString()
        },
        {
            id: '2',
            title: 'Frontend Developer',
            company: 'InnovateCorp',
            location: 'Remote',
            salary: '$100,000 - $130,000',
            description: 'We are looking for a talented Frontend Developer...',
            url: 'https://www.workatastartup.com/jobs/12346',
            platform: 'workatastartup',
            postedDate: new Date().toISOString()
        }
    ];
    // Filter based on criteria
    return mockJobs.filter((job)=>{
        if (criteria.jobTitle && !job.title.toLowerCase().includes(criteria.jobTitle.toLowerCase())) {
            return false;
        }
        // Add more filtering logic as needed
        return true;
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e4015a19._.js.map