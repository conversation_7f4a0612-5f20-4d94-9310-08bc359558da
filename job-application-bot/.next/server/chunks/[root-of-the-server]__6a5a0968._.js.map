{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport EmailProvider from 'next-auth/providers/email';\nimport { PrismaAdapter } from \"@next-auth/prisma-adapter\";\nimport { prisma } from '@/lib/prisma';\n\nconst handler = NextAuth({\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST,\n        port: process.env.EMAIL_SERVER_PORT,\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n      },\n      from: process.env.EMAIL_FROM,\n    }),\n  ],\n  callbacks: {\n    async session({ session, user }) {\n      if (session.user) {\n        session.user.id = user.id;\n      }\n      return session;\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n      }\n      return token;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'database',\n  },\n  events: {\n    async signIn({ user, account, profile, isNewUser }) {\n      console.log('User signed in:', user.email);\n      \n      // Track sign-in event\n      if (user.email) {\n        await prisma.userActivity.create({\n          data: {\n            userId: user.id,\n            action: 'SIGN_IN',\n            details: {\n              provider: account?.provider,\n              isNewUser,\n            },\n          },\n        });\n      }\n    },\n    async signOut({ session }) {\n      console.log('User signed out:', session?.user?.email);\n      \n      // Track sign-out event\n      if (session?.user?.id) {\n        await prisma.userActivity.create({\n          data: {\n            userId: session.user.id,\n            action: 'SIGN_OUT',\n            details: {},\n          },\n        });\n      }\n    },\n  },\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE;IACvB,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,oJAAA,CAAA,UAAa,AAAD,EAAE;YACZ,QAAQ;gBACN,MAAM,QAAQ,GAAG,CAAC,iBAAiB;gBACnC,MAAM,QAAQ,GAAG,CAAC,iBAAiB;gBACnC,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB;oBACnC,MAAM,QAAQ,GAAG,CAAC,qBAAqB;gBACzC;YACF;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU;QAC9B;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;YAC7B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE;YAC3B;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;YAChD,QAAQ,GAAG,CAAC,mBAAmB,KAAK,KAAK;YAEzC,sBAAsB;YACtB,IAAI,KAAK,KAAK,EAAE;gBACd,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,MAAM;wBACJ,QAAQ,KAAK,EAAE;wBACf,QAAQ;wBACR,SAAS;4BACP,UAAU,SAAS;4BACnB;wBACF;oBACF;gBACF;YACF;QACF;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE;YACvB,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAE/C,uBAAuB;YACvB,IAAI,SAAS,MAAM,IAAI;gBACrB,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,MAAM;wBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;wBACvB,QAAQ;wBACR,SAAS,CAAC;oBACZ;gBACF;YACF;QACF;IACF;AACF", "debugId": null}}]}