{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/apply/job-application-bot/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport EmailProvider from 'next-auth/providers/email';\n\nconst handler = NextAuth({\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || '',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',\n    }),\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST || 'smtp.gmail.com',\n        port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),\n        auth: {\n          user: process.env.EMAIL_SERVER_USER || '',\n          pass: process.env.EMAIL_SERVER_PASSWORD || '',\n        },\n      },\n      from: process.env.EMAIL_FROM || '<EMAIL>',\n    }),\n  ],\n  callbacks: {\n    async session({ session, token }) {\n      if (session.user && token.sub) {\n        session.user.id = token.sub;\n      }\n      return session;\n    },\n    async jwt({ token, user, account }) {\n      if (user) {\n        token.id = user.id;\n      }\n      return token;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET || 'fallback-secret-for-development',\n  debug: process.env.NODE_ENV === 'development',\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE;IACvB,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,oJAAA,CAAA,UAAa,AAAD,EAAE;YACZ,QAAQ;gBACN,MAAM,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBACvC,MAAM,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;gBAChD,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB,IAAI;oBACvC,MAAM,QAAQ,GAAG,CAAC,qBAAqB,IAAI;gBAC7C;YACF;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI;QAClC;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,IAAI,MAAM,GAAG,EAAE;gBAC7B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;IACvC,OAAO,oDAAyB;AAClC", "debugId": null}}]}