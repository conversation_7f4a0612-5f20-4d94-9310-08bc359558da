// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  resumes       Resume[]
  applications  JobApplication[]
  settings      UserSettings?
  activities    UserActivity[]
  emailTracking EmailTracking[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Application-specific models
model Resume {
  id           String   @id @default(cuid())
  userId       String
  filename     String
  originalText String   @db.Text
  parsedData   Json
  fileUrl      String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  applications JobApplication[]
}

model Job {
  id              String    @id @default(cuid())
  title           String
  company         String
  location        String
  salary          String?
  description     String    @db.Text
  requirements    String[]  @default([])
  benefits        String[]  @default([])
  url             String
  platform        String
  postedDate      DateTime
  expiryDate      DateTime?
  remote          Boolean   @default(false)
  experienceLevel String
  jobType         String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  applications JobApplication[]

  @@unique([url, platform])
}

model JobApplication {
  id              String            @id @default(cuid())
  userId          String
  jobId           String
  resumeId        String
  coverLetter     String?           @db.Text
  tailoredResume  String?           @db.Text
  status          ApplicationStatus @default(DRAFT)
  appliedDate     DateTime          @default(now())
  lastUpdated     DateTime          @updatedAt
  notes           String?           @db.Text
  followUpDate    DateTime?
  interviewDate   DateTime?
  rejectionReason String?

  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  job           Job             @relation(fields: [jobId], references: [id], onDelete: Cascade)
  resume        Resume          @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  emailTracking EmailTracking[]

  @@unique([userId, jobId])
}

model UserSettings {
  id                       String   @id @default(cuid())
  userId                   String   @unique
  autoApply                Boolean  @default(false)
  emailNotifications       Boolean  @default(true)
  dailyApplicationLimit    Int      @default(10)
  preferredJobTypes        String[] @default([])
  preferredExperienceLevel String   @default("mid")
  blacklistedCompanies     String[] @default([])
  preferredLocations       String[] @default([])
  remoteOnly               Boolean  @default(false)
  minSalary                Int?
  maxApplicationsPerDay    Int      @default(5)
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserActivity {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   Json     @default("{}")
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model EmailTracking {
  id              String      @id @default(cuid())
  userId          String
  applicationId   String?
  emailType       EmailType
  recipient       String
  subject         String
  content         String      @db.Text
  sentAt          DateTime    @default(now())
  deliveredAt     DateTime?
  openedAt        DateTime?
  clickedAt       DateTime?
  repliedAt       DateTime?
  status          EmailStatus @default(SENT)
  trackingPixelId String?     @unique
  metadata        Json        @default("{}")

  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  application JobApplication? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
}

// Enums
enum ApplicationStatus {
  DRAFT
  SUBMITTED
  PENDING
  UNDER_REVIEW
  INTERVIEW_SCHEDULED
  INTERVIEWED
  OFFER_RECEIVED
  ACCEPTED
  REJECTED
  WITHDRAWN
}

enum EmailType {
  APPLICATION
  FOLLOW_UP
  THANK_YOU
  INTERVIEW_CONFIRMATION
  OFFER_RESPONSE
  WITHDRAWAL
  OTHER
}

enum EmailStatus {
  SENT
  DELIVERED
  OPENED
  CLICKED
  REPLIED
  BOUNCED
  FAILED
}
